#include "VolumeRenderer.h"
#include <QDebug>

// VTK includes
#include <vtkGPUVolumeRayCastMapper.h>
#include <vtkFixedPointVolumeRayCastMapper.h>
#include <vtkImageStatistics.h>
#include <vtkProperty.h>
#include <vtkLight.h>

VolumeRenderer::VolumeRenderer(QObject *parent)
    : QObject(parent)
    , m_renderingMode(VolumeRendering)
    , m_isoValue(500.0)
    , m_surfaceColor(Qt::white)
    , m_surfaceOpacity(1.0)
{
    initializeRenderer();
}

VolumeRenderer::~VolumeRenderer()
{
}

void VolumeRenderer::setImageData(vtkSmartPointer<vtkImageData> imageData)
{
    m_imageData = imageData;

    if (m_imageData) {
        // Calculate image statistics for default settings
        double minValue, maxValue, meanValue;
        calculateImageStatistics(minValue, maxValue, meanValue);

        // Set default iso value to mean
        m_isoValue = meanValue;

        // Setup rendering pipelines
        setupVolumeRendering();
        setupSurfaceRendering();

        // Apply default brain preset
        applyBrainPreset();

        // Reset camera to show entire volume
        resetCamera();

        qDebug() << "Image data set for volume rendering";
        qDebug() << "Image range:" << minValue << "to" << maxValue;
        qDebug() << "Default iso value:" << m_isoValue;
    }
}

void VolumeRenderer::setRenderingMode(RenderingMode mode)
{
    if (m_renderingMode == mode) return;

    m_renderingMode = mode;

    // Remove all actors first
    m_renderer->RemoveAllViewProps();

    // Add appropriate actors based on mode
    switch (m_renderingMode) {
        case VolumeRendering:
            if (m_volume) {
                m_renderer->AddVolume(m_volume);
            }
            break;

        case SurfaceRendering:
            if (m_surfaceActor) {
                m_renderer->AddActor(m_surfaceActor);
            }
            break;

        case MixedRendering:
            if (m_volume) {
                m_renderer->AddVolume(m_volume);
            }
            if (m_surfaceActor) {
                m_renderer->AddActor(m_surfaceActor);
                // Make surface semi-transparent for mixed rendering
                m_surfaceActor->GetProperty()->SetOpacity(0.3);
            }
            break;
    }

    updateRendering();
    emit renderingModeChanged(mode);
}

void VolumeRenderer::setOpacityTransferFunction(vtkSmartPointer<vtkPiecewiseFunction> opacity)
{
    m_opacityFunction = opacity;
    if (m_volumeProperty) {
        m_volumeProperty->SetScalarOpacity(m_opacityFunction);
        updateRendering();
    }
}

void VolumeRenderer::setColorTransferFunction(vtkSmartPointer<vtkColorTransferFunction> color)
{
    m_colorFunction = color;
    if (m_volumeProperty) {
        m_volumeProperty->SetColor(m_colorFunction);
        updateRendering();
    }
}

void VolumeRenderer::setIsoValue(double value)
{
    m_isoValue = value;
    if (m_marchingCubes) {
        m_marchingCubes->SetValue(0, m_isoValue);
        updateSurfaceRendering();
    }
}

void VolumeRenderer::setSurfaceColor(const QColor& color)
{
    m_surfaceColor = color;
    if (m_surfaceActor) {
        m_surfaceActor->GetProperty()->SetColor(
            color.redF(), color.greenF(), color.blueF());
        updateRendering();
    }
}

void VolumeRenderer::setSurfaceOpacity(double opacity)
{
    m_surfaceOpacity = opacity;
    if (m_surfaceActor) {
        m_surfaceActor->GetProperty()->SetOpacity(opacity);
        updateRendering();
    }
}

void VolumeRenderer::resetCamera()
{
    if (m_renderer) {
        m_renderer->ResetCamera();
        emit cameraChanged();
    }
}

void VolumeRenderer::setViewDirection(double x, double y, double z)
{
    if (m_renderer && m_renderer->GetActiveCamera()) {
        auto camera = m_renderer->GetActiveCamera();
        double* position = camera->GetPosition();
        double* focalPoint = camera->GetFocalPoint();

        // Calculate new position based on direction
        double distance = camera->GetDistance();
        camera->SetPosition(
            focalPoint[0] + x * distance,
            focalPoint[1] + y * distance,
            focalPoint[2] + z * distance
        );

        emit cameraChanged();
    }
}

void VolumeRenderer::setViewUp(double x, double y, double z)
{
    if (m_renderer && m_renderer->GetActiveCamera()) {
        m_renderer->GetActiveCamera()->SetViewUp(x, y, z);
        emit cameraChanged();
    }
}

void VolumeRenderer::applyBrainPreset()
{
    if (!m_imageData) return;

    // Create brain-specific transfer functions
    createDefaultTransferFunctions();

    // Brain tissue opacity
    m_opacityFunction->RemoveAllPoints();
    m_opacityFunction->AddPoint(0, 0.0);
    m_opacityFunction->AddPoint(100, 0.0);
    m_opacityFunction->AddPoint(200, 0.1);
    m_opacityFunction->AddPoint(500, 0.3);
    m_opacityFunction->AddPoint(1000, 0.8);
    m_opacityFunction->AddPoint(2000, 1.0);

    // Brain tissue colors (grayscale with some contrast)
    m_colorFunction->RemoveAllPoints();
    m_colorFunction->AddRGBPoint(0, 0.0, 0.0, 0.0);      // Black for background
    m_colorFunction->AddRGBPoint(100, 0.1, 0.1, 0.1);    // Dark gray
    m_colorFunction->AddRGBPoint(300, 0.3, 0.3, 0.3);    // Gray matter
    m_colorFunction->AddRGBPoint(600, 0.7, 0.7, 0.7);    // White matter
    m_colorFunction->AddRGBPoint(1000, 1.0, 1.0, 1.0);   // Bright white

    updateVolumeRendering();

    // Set appropriate iso value for brain surface
    setIsoValue(300);
    setSurfaceColor(QColor(200, 200, 200));
    setSurfaceOpacity(0.8);

    qDebug() << "Applied brain preset";
}

void VolumeRenderer::setBackgroundColor(const QColor& color)
{
    if (m_renderer) {
        m_renderer->SetBackground(color.redF(), color.greenF(), color.blueF());
        updateRendering();
    }
}

void VolumeRenderer::updateRendering()
{
    if (m_renderer && m_renderer->GetRenderWindow()) {
        m_renderer->GetRenderWindow()->Render();
        emit renderingUpdated();
    }
}

void VolumeRenderer::initializeRenderer()
{
    m_renderer = vtkSmartPointer<vtkRenderer>::New();
    m_renderer->SetBackground(0.1, 0.1, 0.2); // Dark blue background

    // Add some lighting
    auto light = vtkSmartPointer<vtkLight>::New();
    light->SetPosition(1, 1, 1);
    light->SetIntensity(0.8);
    m_renderer->AddLight(light);
}

void VolumeRenderer::setupVolumeRendering()
{
    if (!m_imageData) return;

    // Create volume mapper (try GPU first, fallback to CPU)
    auto gpuMapper = vtkSmartPointer<vtkGPUVolumeRayCastMapper>::New();
    if (gpuMapper->IsRenderSupported(m_renderer->GetRenderWindow(), m_volumeProperty)) {
        m_volumeMapper = gpuMapper;
        qDebug() << "Using GPU volume ray cast mapper";
    } else {
        m_volumeMapper = vtkSmartPointer<vtkFixedPointVolumeRayCastMapper>::New();
        qDebug() << "Using fixed point volume ray cast mapper";
    }

    m_volumeMapper->SetInputData(m_imageData);

    // Create volume property
    m_volumeProperty = vtkSmartPointer<vtkVolumeProperty>::New();
    m_volumeProperty->SetInterpolationTypeToLinear();
    m_volumeProperty->ShadeOn();
    m_volumeProperty->SetAmbient(0.4);
    m_volumeProperty->SetDiffuse(0.6);
    m_volumeProperty->SetSpecular(0.2);

    // Create volume
    m_volume = vtkSmartPointer<vtkVolume>::New();
    m_volume->SetMapper(m_volumeMapper);
    m_volume->SetProperty(m_volumeProperty);

    createDefaultTransferFunctions();
}

void VolumeRenderer::setupSurfaceRendering()
{
    if (!m_imageData) return;

    // Create marching cubes filter
    m_marchingCubes = vtkSmartPointer<vtkMarchingCubes>::New();
    m_marchingCubes->SetInputData(m_imageData);
    m_marchingCubes->SetValue(0, m_isoValue);
    m_marchingCubes->ComputeNormalsOn();
    m_marchingCubes->ComputeGradientsOn();

    // Create mapper
    m_surfaceMapper = vtkSmartPointer<vtkPolyDataMapper>::New();
    m_surfaceMapper->SetInputConnection(m_marchingCubes->GetOutputPort());
    m_surfaceMapper->ScalarVisibilityOff();

    // Create actor
    m_surfaceActor = vtkSmartPointer<vtkActor>::New();
    m_surfaceActor->SetMapper(m_surfaceMapper);
    m_surfaceActor->GetProperty()->SetColor(
        m_surfaceColor.redF(), m_surfaceColor.greenF(), m_surfaceColor.blueF());
    m_surfaceActor->GetProperty()->SetOpacity(m_surfaceOpacity);
    m_surfaceActor->GetProperty()->SetSpecular(0.3);
    m_surfaceActor->GetProperty()->SetSpecularPower(20);
}

void VolumeRenderer::createDefaultTransferFunctions()
{
    m_colorFunction = vtkSmartPointer<vtkColorTransferFunction>::New();
    m_opacityFunction = vtkSmartPointer<vtkPiecewiseFunction>::New();

    if (m_volumeProperty) {
        m_volumeProperty->SetColor(m_colorFunction);
        m_volumeProperty->SetScalarOpacity(m_opacityFunction);
    }
}

void VolumeRenderer::updateVolumeRendering()
{
    if (m_volumeMapper) {
        m_volumeMapper->Update();
    }
    updateRendering();
}

void VolumeRenderer::updateSurfaceRendering()
{
    if (m_marchingCubes) {
        m_marchingCubes->Update();
    }
    updateRendering();
}

void VolumeRenderer::calculateImageStatistics(double& minValue, double& maxValue, double& meanValue)
{
    if (!m_imageData) {
        minValue = maxValue = meanValue = 0.0;
        return;
    }

    auto stats = vtkSmartPointer<vtkImageStatistics>::New();
    stats->SetInputData(m_imageData);
    stats->Update();

    minValue = stats->GetMin()[0];
    maxValue = stats->GetMax()[0];
    meanValue = stats->GetMean()[0];
}

// Slots implementation
void VolumeRenderer::onOpacityChanged(double opacity)
{
    setSurfaceOpacity(opacity);
}

void VolumeRenderer::onIsoValueChanged(double isoValue)
{
    setIsoValue(isoValue);
}

void VolumeRenderer::onColorChanged(const QColor& color)
{
    setSurfaceColor(color);
}

void VolumeRenderer::applyBonePreset()
{
    if (!m_imageData) return;

    // Create bone-specific transfer functions
    createDefaultTransferFunctions();

    // Bone opacity
    m_opacityFunction->RemoveAllPoints();
    m_opacityFunction->AddPoint(0, 0.0);
    m_opacityFunction->AddPoint(200, 0.0);
    m_opacityFunction->AddPoint(600, 0.3);
    m_opacityFunction->AddPoint(1200, 0.8);
    m_opacityFunction->AddPoint(2000, 1.0);

    // Bone colors (white to yellow)
    m_colorFunction->RemoveAllPoints();
    m_colorFunction->AddRGBPoint(0, 0.0, 0.0, 0.0);      // Black for background
    m_colorFunction->AddRGBPoint(200, 0.2, 0.1, 0.0);    // Dark brown
    m_colorFunction->AddRGBPoint(600, 0.8, 0.6, 0.4);    // Bone color
    m_colorFunction->AddRGBPoint(1200, 1.0, 0.9, 0.7);   // Light bone
    m_colorFunction->AddRGBPoint(2000, 1.0, 1.0, 1.0);   // White

    updateVolumeRendering();

    // Set appropriate iso value for bone surface
    setIsoValue(600);
    setSurfaceColor(QColor(255, 248, 220)); // Bone white
    setSurfaceOpacity(0.9);

    qDebug() << "Applied bone preset";
}

void VolumeRenderer::applySkinPreset()
{
    if (!m_imageData) return;

    // Create skin-specific transfer functions
    createDefaultTransferFunctions();

    // Skin opacity
    m_opacityFunction->RemoveAllPoints();
    m_opacityFunction->AddPoint(0, 0.0);
    m_opacityFunction->AddPoint(50, 0.0);
    m_opacityFunction->AddPoint(150, 0.4);
    m_opacityFunction->AddPoint(400, 0.8);
    m_opacityFunction->AddPoint(800, 1.0);

    // Skin colors (pink to red)
    m_colorFunction->RemoveAllPoints();
    m_colorFunction->AddRGBPoint(0, 0.0, 0.0, 0.0);      // Black for background
    m_colorFunction->AddRGBPoint(50, 0.3, 0.1, 0.1);     // Dark red
    m_colorFunction->AddRGBPoint(150, 0.8, 0.4, 0.3);    // Skin color
    m_colorFunction->AddRGBPoint(400, 1.0, 0.6, 0.5);    // Light skin
    m_colorFunction->AddRGBPoint(800, 1.0, 0.8, 0.7);    // Very light skin

    updateVolumeRendering();

    // Set appropriate iso value for skin surface
    setIsoValue(150);
    setSurfaceColor(QColor(255, 180, 140)); // Skin color
    setSurfaceOpacity(0.7);

    qDebug() << "Applied skin preset";
}

void VolumeRenderer::applyCustomPreset(const QString& presetName)
{
    // This could load custom presets from configuration files
    qDebug() << "Custom preset not implemented:" << presetName;
}

void VolumeRenderer::setAmbientLighting(double ambient)
{
    if (m_volumeProperty) {
        m_volumeProperty->SetAmbient(ambient);
        updateRendering();
    }
}

void VolumeRenderer::setDiffuseLighting(double diffuse)
{
    if (m_volumeProperty) {
        m_volumeProperty->SetDiffuse(diffuse);
        updateRendering();
    }
}

void VolumeRenderer::setSpecularLighting(double specular)
{
    if (m_volumeProperty) {
        m_volumeProperty->SetSpecular(specular);
        updateRendering();
    }
}

void VolumeRenderer::setVolumeProperty(vtkSmartPointer<vtkVolumeProperty> property)
{
    m_volumeProperty = property;
    if (m_volume) {
        m_volume->SetProperty(m_volumeProperty);
        updateRendering();
    }
}
