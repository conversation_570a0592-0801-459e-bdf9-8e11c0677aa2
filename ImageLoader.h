#ifndef IMAGELOADER_H
#define IMAGELOADER_H

#include <QString>
#include <QObject>
#include <memory>

#ifdef HAVE_ITK
// ITK includes
#include "itkImage.h"
#include "itkImageFileReader.h"
#include "itkGDCMImageIO.h"
#include "itkNiftiImageIO.h"
#include "itkImageSeriesReader.h"
#include "itkGDCMSeriesFileNames.h"
#include "itkRescaleIntensityImageFilter.h"
#endif

#ifdef HAVE_VTK
// VTK includes
#include <vtkImageData.h>
#include <vtkSmartPointer.h>
#endif

class ImageLoader : public QObject
{
    Q_OBJECT

public:
#ifdef HAVE_ITK
    // Image types
    using PixelType = float;
    using ImageType = itk::Image<PixelType, 3>;
    using ImagePointer = ImageType::Pointer;
#endif

    explicit ImageLoader(QObject *parent = nullptr);
    ~ImageLoader();

    // Load single NIfTI file
    bool loadNiftiFile(const QString& filePath);

    // Load DICOM series from directory
    bool loadDicomSeries(const QString& directoryPath);

    // Load single DICOM file
    bool loadDicomFile(const QString& filePath);

#ifdef HAVE_ITK
    // Get loaded image
    ImagePointer getITKImage() const { return m_image; }

    // Get image dimensions and spacing
    ImageType::SizeType getImageSize() const;
    ImageType::SpacingType getImageSpacing() const;
    ImageType::PointType getImageOrigin() const;
#endif

#ifdef HAVE_VTK
    // Convert ITK image to VTK format
    vtkSmartPointer<vtkImageData> getVTKImage();
#endif

    // Get image information
    QString getImageInfo() const;
    bool isImageLoaded() const;

signals:
    void imageLoaded(const QString& info);
    void loadingError(const QString& error);
    void loadingProgress(int percentage);

private:
#ifdef HAVE_ITK
    ImagePointer m_image;
#endif

#ifdef HAVE_VTK
    vtkSmartPointer<vtkImageData> m_vtkImage;
#endif

    // State tracking
    bool m_imageLoaded;
    QString m_currentImagePath;

    // Helper methods
#ifdef HAVE_VTK
    void convertITKToVTK();
#endif
    QString formatImageInfo() const;

#ifdef HAVE_ITK
    // Template method for loading different image types
    template<typename TImageType>
    bool loadImageFile(const QString& filePath, typename itk::ImageIOBase::Pointer imageIO);
#endif
};

#endif // IMAGELOADER_H
