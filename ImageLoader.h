#ifndef IMAGELOADER_H
#define IMAGELOADER_H

#include <QString>
#include <QObject>
#include <memory>

// ITK includes
#include "itkImage.h"
#include "itkImageFileReader.h"
#include "itkGDCMImageIO.h"
#include "itkNiftiImageIO.h"
#include "itkImageSeriesReader.h"
#include "itkGDCMSeriesFileNames.h"
#include "itkRescaleIntensityImageFilter.h"

// VTK includes
#include <vtkImageData.h>
#include <vtkSmartPointer.h>

class ImageLoader : public QObject
{
    Q_OBJECT

public:
    // Image types
    using PixelType = float;
    using ImageType = itk::Image<PixelType, 3>;
    using ImagePointer = ImageType::Pointer;
    
    explicit ImageLoader(QObject *parent = nullptr);
    ~ImageLoader();
    
    // Load single NIfTI file
    bool loadNiftiFile(const QString& filePath);
    
    // Load DICOM series from directory
    bool loadDicomSeries(const QString& directoryPath);
    
    // Load single DICOM file
    bool loadDicomFile(const QString& filePath);
    
    // Get loaded image
    ImagePointer getITKImage() const { return m_image; }
    
    // Convert ITK image to VTK format
    vtkSmartPointer<vtkImageData> getVTKImage();
    
    // Get image information
    QString getImageInfo() const;
    bool isImageLoaded() const { return m_image.IsNotNull(); }
    
    // Get image dimensions and spacing
    ImageType::SizeType getImageSize() const;
    ImageType::SpacingType getImageSpacing() const;
    ImageType::PointType getImageOrigin() const;
    
signals:
    void imageLoaded(const QString& info);
    void loadingError(const QString& error);
    void loadingProgress(int percentage);

private:
    ImagePointer m_image;
    vtkSmartPointer<vtkImageData> m_vtkImage;
    
    // Helper methods
    void convertITKToVTK();
    QString formatImageInfo() const;
    
    // Template method for loading different image types
    template<typename TImageType>
    bool loadImageFile(const QString& filePath, typename itk::ImageIOBase::Pointer imageIO);
};

#endif // IMAGELOADER_H
