#include "ImageLoader.h"
#include <QDebug>
#include <QDir>
#include <QFileInfo>

// ITK includes
#include "itkImageToVTKImageFilter.h"
#include "itkCastImageFilter.h"

ImageLoader::ImageLoader(QObject *parent)
    : QObject(parent)
    , m_image(nullptr)
    , m_vtkImage(nullptr)
{
}

ImageLoader::~ImageLoader()
{
}

bool ImageLoader::loadNiftiFile(const QString& filePath)
{
    try {
        emit loadingProgress(10);
        
        // Create NIfTI image IO
        auto niftiIO = itk::NiftiImageIO::New();
        
        emit loadingProgress(30);
        
        // Create reader
        using ReaderType = itk::ImageFileReader<ImageType>;
        auto reader = ReaderType::New();
        reader->SetFileName(filePath.toStdString());
        reader->SetImageIO(niftiIO);
        
        emit loadingProgress(50);
        
        // Read the image
        reader->Update();
        m_image = reader->GetOutput();
        
        emit loadingProgress(80);
        
        // Convert to VTK format
        convertITKToVTK();
        
        emit loadingProgress(100);
        emit imageLoaded(getImageInfo());
        
        qDebug() << "Successfully loaded NIfTI file:" << filePath;
        return true;
        
    } catch (const itk::ExceptionObject& ex) {
        QString error = QString("Failed to load NIfTI file: %1").arg(ex.GetDescription());
        emit loadingError(error);
        qDebug() << error;
        return false;
    }
}

bool ImageLoader::loadDicomSeries(const QString& directoryPath)
{
    try {
        emit loadingProgress(10);
        
        // Get DICOM series file names
        using NamesGeneratorType = itk::GDCMSeriesFileNames;
        auto nameGenerator = NamesGeneratorType::New();
        nameGenerator->SetUseSeriesDetails(true);
        nameGenerator->AddSeriesRestriction("0008|0021"); // Series Date
        nameGenerator->SetDirectory(directoryPath.toStdString());
        
        emit loadingProgress(30);
        
        // Get series UIDs
        using SeriesIdContainer = std::vector<std::string>;
        const SeriesIdContainer& seriesUID = nameGenerator->GetSeriesUIDs();
        
        if (seriesUID.empty()) {
            emit loadingError("No DICOM series found in directory");
            return false;
        }
        
        // Use the first series
        std::string seriesIdentifier = seriesUID.begin()->c_str();
        using FileNamesContainer = std::vector<std::string>;
        FileNamesContainer fileNames = nameGenerator->GetFileNames(seriesIdentifier);
        
        emit loadingProgress(50);
        
        // Create DICOM image IO
        using ImageIOType = itk::GDCMImageIO;
        auto dicomIO = ImageIOType::New();
        
        // Create series reader
        using ReaderType = itk::ImageSeriesReader<ImageType>;
        auto reader = ReaderType::New();
        reader->SetImageIO(dicomIO);
        reader->SetFileNames(fileNames);
        
        emit loadingProgress(70);
        
        // Read the series
        reader->Update();
        m_image = reader->GetOutput();
        
        emit loadingProgress(90);
        
        // Convert to VTK format
        convertITKToVTK();
        
        emit loadingProgress(100);
        emit imageLoaded(getImageInfo());
        
        qDebug() << "Successfully loaded DICOM series from:" << directoryPath;
        return true;
        
    } catch (const itk::ExceptionObject& ex) {
        QString error = QString("Failed to load DICOM series: %1").arg(ex.GetDescription());
        emit loadingError(error);
        qDebug() << error;
        return false;
    }
}

bool ImageLoader::loadDicomFile(const QString& filePath)
{
    try {
        emit loadingProgress(10);
        
        // Create DICOM image IO
        auto dicomIO = itk::GDCMImageIO::New();
        
        emit loadingProgress(30);
        
        // Create reader
        using ReaderType = itk::ImageFileReader<ImageType>;
        auto reader = ReaderType::New();
        reader->SetFileName(filePath.toStdString());
        reader->SetImageIO(dicomIO);
        
        emit loadingProgress(50);
        
        // Read the image
        reader->Update();
        m_image = reader->GetOutput();
        
        emit loadingProgress(80);
        
        // Convert to VTK format
        convertITKToVTK();
        
        emit loadingProgress(100);
        emit imageLoaded(getImageInfo());
        
        qDebug() << "Successfully loaded DICOM file:" << filePath;
        return true;
        
    } catch (const itk::ExceptionObject& ex) {
        QString error = QString("Failed to load DICOM file: %1").arg(ex.GetDescription());
        emit loadingError(error);
        qDebug() << error;
        return false;
    }
}

vtkSmartPointer<vtkImageData> ImageLoader::getVTKImage()
{
    return m_vtkImage;
}

QString ImageLoader::getImageInfo() const
{
    if (!m_image) {
        return "No image loaded";
    }
    
    return formatImageInfo();
}

ImageLoader::ImageType::SizeType ImageLoader::getImageSize() const
{
    if (m_image) {
        return m_image->GetLargestPossibleRegion().GetSize();
    }
    return ImageType::SizeType();
}

ImageLoader::ImageType::SpacingType ImageLoader::getImageSpacing() const
{
    if (m_image) {
        return m_image->GetSpacing();
    }
    return ImageType::SpacingType();
}

ImageLoader::ImageType::PointType ImageLoader::getImageOrigin() const
{
    if (m_image) {
        return m_image->GetOrigin();
    }
    return ImageType::PointType();
}

void ImageLoader::convertITKToVTK()
{
    if (!m_image) {
        return;
    }
    
    try {
        // Use ITK to VTK filter
        using FilterType = itk::ImageToVTKImageFilter<ImageType>;
        auto filter = FilterType::New();
        filter->SetInput(m_image);
        filter->Update();
        
        m_vtkImage = vtkSmartPointer<vtkImageData>::New();
        m_vtkImage->DeepCopy(filter->GetOutput());
        
    } catch (const itk::ExceptionObject& ex) {
        qDebug() << "Error converting ITK to VTK:" << ex.GetDescription();
    }
}

QString ImageLoader::formatImageInfo() const
{
    if (!m_image) {
        return "No image loaded";
    }
    
    auto size = m_image->GetLargestPossibleRegion().GetSize();
    auto spacing = m_image->GetSpacing();
    auto origin = m_image->GetOrigin();
    
    QString info = QString(
        "Image Information:\n"
        "Dimensions: %1 x %2 x %3\n"
        "Spacing: %.2f x %.2f x %.2f mm\n"
        "Origin: (%.2f, %.2f, %.2f)\n"
        "Pixel Type: %4"
    ).arg(size[0]).arg(size[1]).arg(size[2])
     .arg(spacing[0]).arg(spacing[1]).arg(spacing[2])
     .arg(origin[0]).arg(origin[1]).arg(origin[2])
     .arg("Float");
    
    return info;
}
