{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-9a9e12c9e8625452dddf.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "qt_ni", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "qt_ni::@6890427a1f51a3e7e1df", "jsonFile": "target-qt_ni-Debug-34094f219e6872612bfc.json", "name": "qt_ni", "projectIndex": 0}, {"directoryIndex": 0, "id": "qt_ni_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-qt_ni_autogen-Debug-4dee6d7f115487250574.json", "name": "qt_ni_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "qt_ni_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-qt_ni_autogen_timestamp_deps-Debug-2c74c4a3375e6b85be32.json", "name": "qt_ni_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "source": "C:/Users/<USER>/Desktop/desktop_projects/qt_ni"}, "version": {"major": 2, "minor": 7}}