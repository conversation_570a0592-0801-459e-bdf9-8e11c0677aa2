﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{4299A5DC-DB1D-379D-853A-51A1C6387E78}"
	ProjectSection(ProjectDependencies) = postProject
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438} = {87FCF19B-09BA-3CFF-9247-ECAE3989A438}
		{606EA022-C81A-388D-A91A-347F8F6FC089} = {606EA022-C81A-388D-A91A-347F8F6FC089}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{17A37EF0-B651-3222-8C4E-B12DE851D4EF}"
	ProjectSection(ProjectDependencies) = postProject
		{4299A5DC-DB1D-379D-853A-51A1C6387E78} = {4299A5DC-DB1D-379D-853A-51A1C6387E78}
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438} = {87FCF19B-09BA-3CFF-9247-ECAE3989A438}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{87FCF19B-09BA-3CFF-9247-ECAE3989A438}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "qt_ni", "qt_ni.vcxproj", "{606EA022-C81A-388D-A91A-347F8F6FC089}"
	ProjectSection(ProjectDependencies) = postProject
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438} = {87FCF19B-09BA-3CFF-9247-ECAE3989A438}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4299A5DC-DB1D-379D-853A-51A1C6387E78}.Debug|x64.ActiveCfg = Debug|x64
		{4299A5DC-DB1D-379D-853A-51A1C6387E78}.Debug|x64.Build.0 = Debug|x64
		{4299A5DC-DB1D-379D-853A-51A1C6387E78}.Release|x64.ActiveCfg = Release|x64
		{4299A5DC-DB1D-379D-853A-51A1C6387E78}.Release|x64.Build.0 = Release|x64
		{4299A5DC-DB1D-379D-853A-51A1C6387E78}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4299A5DC-DB1D-379D-853A-51A1C6387E78}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4299A5DC-DB1D-379D-853A-51A1C6387E78}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4299A5DC-DB1D-379D-853A-51A1C6387E78}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{17A37EF0-B651-3222-8C4E-B12DE851D4EF}.Debug|x64.ActiveCfg = Debug|x64
		{17A37EF0-B651-3222-8C4E-B12DE851D4EF}.Release|x64.ActiveCfg = Release|x64
		{17A37EF0-B651-3222-8C4E-B12DE851D4EF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{17A37EF0-B651-3222-8C4E-B12DE851D4EF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438}.Debug|x64.ActiveCfg = Debug|x64
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438}.Debug|x64.Build.0 = Debug|x64
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438}.Release|x64.ActiveCfg = Release|x64
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438}.Release|x64.Build.0 = Release|x64
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{87FCF19B-09BA-3CFF-9247-ECAE3989A438}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{606EA022-C81A-388D-A91A-347F8F6FC089}.Debug|x64.ActiveCfg = Debug|x64
		{606EA022-C81A-388D-A91A-347F8F6FC089}.Debug|x64.Build.0 = Debug|x64
		{606EA022-C81A-388D-A91A-347F8F6FC089}.Release|x64.ActiveCfg = Release|x64
		{606EA022-C81A-388D-A91A-347F8F6FC089}.Release|x64.Build.0 = Release|x64
		{606EA022-C81A-388D-A91A-347F8F6FC089}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{606EA022-C81A-388D-A91A-347F8F6FC089}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{606EA022-C81A-388D-A91A-347F8F6FC089}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{606EA022-C81A-388D-A91A-347F8F6FC089}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {302BCD65-DE8B-38C3-BCDA-8866B64AAD90}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
