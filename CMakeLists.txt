cmake_minimum_required(VERSION 3.16)

project(qt_ni VERSION 0.1 LANGUAGES CXX)

# Windows-specific settings
if(WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
    set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
endif()

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Windows MSVC specific settings
if(WIN32 AND MSVC)
    add_compile_options(/Zc:__cplusplus)
    add_compile_options(/std:c++17)
endif()

# Find Qt packages
find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Widgets OpenGL)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Widgets OpenGL)

# Try to find VTK (optional for Windows)
find_package(VTK QUIET)
if(VTK_FOUND)
    message(STATUS "VTK found: ${VTK_VERSION}")
    if(VTK_VERSION VERSION_GREATER_EQUAL "9.0")
        # VTK 9.x style
        set(HAVE_VTK TRUE)
    else()
        # VTK 8.x style
        include(${VTK_USE_FILE})
        set(HAVE_VTK TRUE)
    endif()
else()
    message(STATUS "VTK not found - 3D visualization features will be limited")
    set(HAVE_VTK FALSE)
endif()

# Try to find ITK (optional for Windows)
find_package(ITK QUIET)
if(ITK_FOUND)
    message(STATUS "ITK found: ${ITK_VERSION}")
    include(${ITK_USE_FILE})
    set(HAVE_ITK TRUE)
else()
    message(STATUS "ITK not found - medical image loading features will be limited")
    set(HAVE_ITK FALSE)
endif()

set(PROJECT_SOURCES
        main.cpp
        mainwindow.cpp
        mainwindow.h
        mainwindow.ui
        ImageLoader.cpp
        ImageLoader.h
        VolumeRenderer.cpp
        VolumeRenderer.h
        BrainAtlas.cpp
        BrainAtlas.h
        ViewerWidget.cpp
        ViewerWidget.h
)

if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_executable(qt_ni
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}
    )
# Define target properties for Android with Qt 6 as:
#    set_property(TARGET qt_ni APPEND PROPERTY QT_ANDROID_PACKAGE_SOURCE_DIR
#                 ${CMAKE_CURRENT_SOURCE_DIR}/android)
# For more information, see https://doc.qt.io/qt-6/qt-add-executable.html#target-creation
else()
    if(ANDROID)
        add_library(qt_ni SHARED
            ${PROJECT_SOURCES}
        )
# Define properties for Android with Qt 5 after find_package() calls as:
#    set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android")
    else()
        add_executable(qt_ni
            ${PROJECT_SOURCES}
        )
    endif()
endif()

# Configure preprocessor definitions
if(HAVE_VTK)
    target_compile_definitions(qt_ni PRIVATE HAVE_VTK)
endif()

if(HAVE_ITK)
    target_compile_definitions(qt_ni PRIVATE HAVE_ITK)
endif()

# Link libraries
target_link_libraries(qt_ni PRIVATE
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::OpenGL
)

# Link VTK libraries if available
if(HAVE_VTK)
    target_link_libraries(qt_ni PRIVATE ${VTK_LIBRARIES})
endif()

# Link ITK libraries if available
if(HAVE_ITK)
    target_link_libraries(qt_ni PRIVATE ${ITK_LIBRARIES})
endif()

# Qt for iOS sets MACOSX_BUNDLE_GUI_IDENTIFIER automatically since Qt 6.1.
# If you are developing for iOS or macOS you should consider setting an
# explicit, fixed bundle identifier manually though.
if(${QT_VERSION} VERSION_LESS 6.1.0)
  set(BUNDLE_ID_OPTION MACOSX_BUNDLE_GUI_IDENTIFIER com.example.qt_ni)
endif()
set_target_properties(qt_ni PROPERTIES
    ${BUNDLE_ID_OPTION}
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
)

include(GNUInstallDirs)
install(TARGETS qt_ni
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(qt_ni)
endif()
