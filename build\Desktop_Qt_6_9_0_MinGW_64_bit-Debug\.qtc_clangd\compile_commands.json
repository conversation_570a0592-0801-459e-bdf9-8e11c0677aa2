[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\desktop_projects\\qt_ni\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\qt_ni_autogen\\include", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\desktop_projects\\qt_ni\\main.cpp"], "directory": "C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/desktop_projects/qt_ni/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\desktop_projects\\qt_ni\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\qt_ni_autogen\\include", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\desktop_projects\\qt_ni\\mainwindow.cpp"], "directory": "C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/desktop_projects/qt_ni/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\desktop_projects\\qt_ni\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\qt_ni_autogen\\include", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\desktop_projects\\qt_ni\\mainwindow.h"], "directory": "C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/desktop_projects/qt_ni/mainwindow.h"}]