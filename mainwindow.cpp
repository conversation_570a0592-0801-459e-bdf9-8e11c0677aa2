#include "mainwindow.h"
#include "./ui_mainwindow.h"
#include <QApplication>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_viewerWidget(nullptr)
{
    ui->setupUi(this);

    // Set window properties
    setWindowTitle("Medical Image 3D Viewer with Brain Atlas");
    setMinimumSize(1200, 800);
    resize(1400, 900);

    // Create and set central widget
    m_viewerWidget = new ViewerWidget(this);
    setCentralWidget(m_viewerWidget);

    // Setup UI components
    setupActions();
    setupMenus();
    setupStatusBar();
    connectSignals();

    // Show welcome message
    statusBar()->showMessage("Ready - Load a medical image to begin", 5000);
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupActions()
{
    // File menu actions
    m_openImageAction = new QAction("&Open Image...", this);
    m_openImageAction->setShortcut(QKeySequence::Open);
    m_openImageAction->setStatusTip("Open a medical image file (NIfTI or DICOM)");

    m_openDicomSeriesAction = new QAction("Open &DICOM Series...", this);
    m_openDicomSeriesAction->setShortcut(QKeySequence("Ctrl+D"));
    m_openDicomSeriesAction->setStatusTip("Open a DICOM series from directory");

    m_loadAtlasAction = new QAction("Load &Atlas...", this);
    m_loadAtlasAction->setShortcut(QKeySequence("Ctrl+A"));
    m_loadAtlasAction->setStatusTip("Load brain atlas for region labeling");

    m_exitAction = new QAction("E&xit", this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    m_exitAction->setStatusTip("Exit the application");

    // Help menu actions
    m_aboutAction = new QAction("&About", this);
    m_aboutAction->setStatusTip("Show information about this application");
}

void MainWindow::setupMenus()
{
    // File menu
    QMenu *fileMenu = menuBar()->addMenu("&File");
    fileMenu->addAction(m_openImageAction);
    fileMenu->addAction(m_openDicomSeriesAction);
    fileMenu->addSeparator();
    fileMenu->addAction(m_loadAtlasAction);
    fileMenu->addSeparator();
    fileMenu->addAction(m_exitAction);

    // Help menu
    QMenu *helpMenu = menuBar()->addMenu("&Help");
    helpMenu->addAction(m_aboutAction);
}

void MainWindow::setupStatusBar()
{
    m_statusLabel = new QLabel("Ready");
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    m_progressBar->setMaximumWidth(200);

    statusBar()->addWidget(m_statusLabel, 1);
    statusBar()->addPermanentWidget(m_progressBar);
}

void MainWindow::connectSignals()
{
    // Menu actions
    connect(m_openImageAction, &QAction::triggered, this, &MainWindow::onOpenImage);
    connect(m_openDicomSeriesAction, &QAction::triggered, this, &MainWindow::onOpenDicomSeries);
    connect(m_loadAtlasAction, &QAction::triggered, this, &MainWindow::onLoadAtlas);
    connect(m_exitAction, &QAction::triggered, this, &MainWindow::onExit);
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::onAbout);

    // Viewer widget signals
    if (m_viewerWidget) {
        connect(m_viewerWidget, &ViewerWidget::imageLoaded,
                this, &MainWindow::onImageLoaded);
        connect(m_viewerWidget, &ViewerWidget::atlasLoaded,
                this, &MainWindow::onAtlasLoaded);
        connect(m_viewerWidget, &ViewerWidget::regionClicked,
                this, &MainWindow::onRegionClicked);
        connect(m_viewerWidget, &ViewerWidget::loadingError,
                this, &MainWindow::onLoadingError);
        connect(m_viewerWidget, &ViewerWidget::statusMessage,
                this, &MainWindow::onStatusMessage);
    }
}

// Slot implementations
void MainWindow::onOpenImage()
{
    if (m_viewerWidget) {
        m_viewerWidget->onLoadImageClicked();
    }
}

void MainWindow::onOpenDicomSeries()
{
    if (m_viewerWidget) {
        m_viewerWidget->onLoadDicomSeriesClicked();
    }
}

void MainWindow::onLoadAtlas()
{
    if (m_viewerWidget) {
        m_viewerWidget->onLoadAtlasClicked();
    }
}

void MainWindow::onAbout()
{
    QMessageBox::about(this, "About Medical Image 3D Viewer",
        "<h2>Medical Image 3D Viewer with Brain Atlas</h2>"
        "<p>Version 1.0</p>"
        "<p>A comprehensive 3D medical image visualization tool with brain atlas integration.</p>"
        "<p><b>Features:</b></p>"
        "<ul>"
        "<li>Support for NIfTI and DICOM formats</li>"
        "<li>Volume and surface rendering</li>"
        "<li>Brain atlas overlay with region labeling</li>"
        "<li>Interactive 3D visualization</li>"
        "<li>Multiple rendering presets</li>"
        "</ul>"
        "<p><b>Libraries used:</b></p>"
        "<ul>"
        "<li>Qt for user interface</li>"
        "<li>VTK for 3D visualization</li>"
        "<li>ITK for medical image processing</li>"
        "</ul>"
        "<p>Developed for medical imaging research and education.</p>"
    );
}

void MainWindow::onExit()
{
    QApplication::quit();
}

void MainWindow::onImageLoaded(const QString& info)
{
    m_statusLabel->setText("Image loaded successfully");
    statusBar()->showMessage("Image loaded - Ready for visualization", 3000);
}

void MainWindow::onAtlasLoaded(const QString& info)
{
    m_statusLabel->setText("Atlas loaded successfully");
    statusBar()->showMessage("Brain atlas loaded - Regions available for labeling", 3000);
}

void MainWindow::onRegionClicked(int regionId, const QString& regionName)
{
    QString message = QString("Selected region: %1 (ID: %2)").arg(regionName).arg(regionId);
    statusBar()->showMessage(message, 5000);
}

void MainWindow::onLoadingError(const QString& error)
{
    m_statusLabel->setText("Error occurred");
    statusBar()->showMessage("Error: " + error, 10000);
    m_progressBar->setVisible(false);
}

void MainWindow::onStatusMessage(const QString& message)
{
    m_statusLabel->setText(message);
    statusBar()->showMessage(message, 3000);
}
