# Windows 安装和使用指南

## 医学图像3D可视化工具 - Windows版本

本指南将帮助您在Windows系统上安装和使用这个医学图像3D可视化工具。

## 系统要求

### 最低要求
- **操作系统**: Windows 10 (1903) 或更高版本
- **内存**: 8GB RAM (推荐16GB)
- **存储**: 2GB可用空间
- **显卡**: 支持OpenGL 3.3的显卡
- **处理器**: Intel i5或AMD Ryzen 5以上

### 推荐配置
- **操作系统**: Windows 11
- **内存**: 16GB RAM或更多
- **显卡**: 独立显卡 (NVIDIA GTX 1060或AMD RX 580以上)
- **处理器**: Intel i7或AMD Ryzen 7以上

## 快速开始 (仅使用Qt基础功能)

如果您只想快速体验基本功能，可以先安装Qt并编译基础版本：

### 1. 安装Qt
1. 访问 [Qt官网](https://www.qt.io/download-qt-installer)
2. 下载Qt Online Installer
3. 安装时选择：
   - Qt 6.5.0 或更高版本
   - MinGW 11.2.0 64-bit 编译器
   - Qt Creator IDE

### 2. 编译基础版本
```bash
# 打开Qt Creator命令提示符
cd C:\Users\<USER>\Desktop\desktop_projects\qt_ni
mkdir build
cd build

# 配置项目
cmake .. -G "MinGW Makefiles" -DCMAKE_PREFIX_PATH="C:/Qt/6.9.0/mingw_64"

# 编译
cmake --build . --config Release

# 运行
./qt_ni.exe
```

## 完整功能安装 (包含VTK和ITK)

要使用完整的3D可视化和医学图像处理功能，需要安装VTK和ITK库：

### 方法1: 使用vcpkg (推荐)

#### 安装vcpkg
```bash
# 克隆vcpkg
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg

# 运行bootstrap脚本
.\bootstrap-vcpkg.bat

# 集成到Visual Studio
.\vcpkg integrate install
```

#### 安装依赖库
```bash
# 安装VTK
.\vcpkg install vtk[qt]:x64-windows

# 安装ITK
.\vcpkg install itk:x64-windows

# 安装Qt (如果还没安装)
.\vcpkg install qt5-base:x64-windows
```

#### 编译项目
```bash
cd C:\Users\<USER>\Desktop\desktop_projects\qt_ni
mkdir build
cd build

# 使用vcpkg工具链配置
cmake .. -DCMAKE_TOOLCHAIN_FILE=C:/path/to/vcpkg/scripts/buildsystems/vcpkg.cmake

# 编译
cmake --build . --config Release
```

### 方法2: 手动安装预编译库

#### 下载预编译库
1. **VTK**: 从 [VTK官网](https://vtk.org/download/) 下载Windows预编译版本
2. **ITK**: 从 [ITK官网](https://itk.org/download/) 下载Windows预编译版本

#### 配置环境变量
```bash
# 添加到系统环境变量
VTK_DIR=C:\VTK\lib\cmake\vtk-9.2
ITK_DIR=C:\ITK\lib\cmake\ITK-5.3
```

#### 编译项目
```bash
cmake .. -DVTK_DIR=%VTK_DIR% -DITK_DIR=%ITK_DIR% -DCMAKE_PREFIX_PATH="C:/Qt/6.9.0/mingw_64"
cmake --build . --config Release
```

## 使用指南

### 启动应用程序
1. 双击 `qt_ni.exe` 启动程序
2. 程序界面分为三个主要区域：
   - 左侧：控制面板
   - 中间：3D可视化区域
   - 右侧：信息面板

### 加载医学图像

#### 加载NIfTI文件
1. 点击"Load NIfTI/DICOM File"按钮
2. 选择 `.nii` 或 `.nii.gz` 文件
3. 等待加载完成

#### 加载DICOM系列
1. 点击"Load DICOM Series"按钮
2. 选择包含DICOM文件的文件夹
3. 程序会自动检测并加载整个系列

### 3D可视化操作

#### 鼠标控制
- **旋转**: 左键拖拽
- **缩放**: 滚轮或右键拖拽
- **平移**: 中键拖拽

#### 渲染模式
- **Volume Rendering**: 体绘制，显示内部结构
- **Surface Rendering**: 表面绘制，显示等值面
- **Mixed Rendering**: 混合模式

#### 参数调节
- **Opacity**: 调节透明度 (0-100%)
- **Iso Value**: 调节等值面阈值
- **Preset**: 选择预设配置 (Brain/Bone/Skin)

### 脑图谱功能

#### 加载标准图谱
1. 从下拉菜单选择图谱类型 (AAL, AAL2, AAL3等)
2. 点击"Load Atlas"按钮
3. 程序会尝试从默认位置加载图谱

#### 加载自定义图谱
1. 选择"Custom"图谱类型
2. 点击"Load Atlas"按钮
3. 选择图谱图像文件 (.nii)
4. 选择对应的标签文件 (.txt)

#### 脑区交互
- **显示/隐藏脑区**: 勾选"Show Atlas Regions"
- **显示/隐藏标签**: 勾选"Show Region Labels"
- **调节透明度**: 使用"Atlas Opacity"滑块
- **选择脑区**: 点击3D视图中的脑区查看详细信息

## 故障排除

### 常见问题

#### 程序无法启动
- 检查是否安装了Visual C++ Redistributable
- 确认Qt库路径正确
- 检查系统是否支持OpenGL 3.3

#### 图像加载失败
- 确认文件格式正确 (.nii, .nii.gz, .dcm)
- 检查文件是否损坏
- 确认有足够的内存加载大文件

#### 3D渲染性能差
- 更新显卡驱动
- 降低图像分辨率
- 关闭其他占用GPU的程序
- 调低渲染质量设置

#### VTK/ITK库找不到
- 检查环境变量设置
- 确认库版本兼容性
- 重新安装依赖库

### 性能优化建议

#### 硬件优化
- 使用SSD存储提高文件加载速度
- 增加内存容量处理大数据集
- 使用独立显卡提升渲染性能

#### 软件优化
- 关闭不必要的后台程序
- 调整Windows电源计划为"高性能"
- 定期清理临时文件

## 示例数据

### 获取测试数据
1. **NIfTI样本**: 可从 [NITRC](https://www.nitrc.org/) 下载
2. **DICOM样本**: 可从 [TCIA](https://www.cancerimagingarchive.net/) 下载
3. **脑图谱**: 可从 [SPM](https://www.fil.ion.ucl.ac.uk/spm/) 下载AAL图谱

### 推荐的测试文件
- 结构MRI: T1加权图像
- 功能MRI: BOLD信号图像
- DTI: 扩散张量图像

## 技术支持

### 获取帮助
- 查看程序内置帮助: Help → About
- 检查日志文件: 程序目录下的 `debug.log`
- 访问项目文档和FAQ

### 报告问题
提交问题时请包含：
- 操作系统版本
- 程序版本信息
- 错误信息截图
- 问题重现步骤
- 相关日志文件

## 更新和维护

### 检查更新
- 程序会自动检查更新
- 手动检查: Help → Check for Updates

### 备份设置
- 配置文件位置: `%APPDATA%/qt_ni/`
- 建议定期备份配置和自定义图谱

---

**注意**: 本程序仅用于研究和教育目的，不可用于临床诊断。
