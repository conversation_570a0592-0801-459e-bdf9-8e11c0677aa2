﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\mainwindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\ImageLoader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\VolumeRenderer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\BrainAtlas.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\ViewerWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\ImageLoader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\VolumeRenderer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\BrainAtlas.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\ViewerWidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_Debug\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_Release\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_MinSizeRel\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_RelWithDebInfo\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\6932cf1e8580741708e9dc78e096639f\autouic_(CONFIG).stamp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\mainwindow.ui" />
    <None Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_Debug.stamp" />
    <None Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_Release.stamp" />
    <None Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_MinSizeRel.stamp" />
    <None Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_RelWithDebInfo.stamp" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{935A5BB9-582E-319B-A1FE-D69FF67C755B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{33257AAA-35E0-3098-B600-6F9B5E2589E8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{82222BA7-586F-3540-A0D6-5ED8CC429CD4}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
