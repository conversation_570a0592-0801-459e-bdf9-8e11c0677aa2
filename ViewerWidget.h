#ifndef VIEWERWIDGET_H
#define VIEWERWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QSlider>
#include <QLabel>
#include <QComboBox>
#include <QCheckBox>
#include <QGroupBox>
#include <QProgressBar>
#include <QTextEdit>
#include <QSplitter>

// VTK includes
#include <QVTKOpenGLNativeWidget.h>
#include <vtkSmartPointer.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkInteractorStyleTrackballCamera.h>

// Custom includes
#include "ImageLoader.h"
#include "VolumeRenderer.h"
#include "BrainAtlas.h"

class ViewerWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ViewerWidget(QWidget *parent = nullptr);
    ~ViewerWidget();
    
    // Image loading
    void loadImage(const QString& filePath);
    void loadDicomSeries(const QString& directoryPath);
    
    // Atlas management
    void loadAtlas(BrainAtlas::AtlasType atlasType, const QString& atlasPath = "");
    void loadCustomAtlas(const QString& atlasImagePath, const QString& labelFilePath);
    
    // Rendering control
    void setRenderingMode(VolumeRenderer::RenderingMode mode);
    void resetView();
    
    // Get current state
    bool isImageLoaded() const;
    bool isAtlasLoaded() const;
    QString getImageInfo() const;
    QString getAtlasInfo() const;

signals:
    void imageLoaded(const QString& info);
    void atlasLoaded(const QString& info);
    void regionClicked(int regionId, const QString& regionName);
    void loadingError(const QString& error);
    void statusMessage(const QString& message);

public slots:
    void onLoadImageClicked();
    void onLoadDicomSeriesClicked();
    void onLoadAtlasClicked();
    void onRenderingModeChanged();
    void onResetViewClicked();
    void onOpacityChanged(int value);
    void onIsoValueChanged(int value);
    void onAtlasOpacityChanged(int value);
    void onShowLabelsToggled(bool show);
    void onShowAtlasToggled(bool show);
    void onPresetChanged();

private slots:
    void onImageLoadingProgress(int percentage);
    void onImageLoadingError(const QString& error);
    void onAtlasLoadingError(const QString& error);
    void onRegionClicked(int regionId, const QString& regionName);

private:
    // UI components
    void setupUI();
    void setupControlPanel();
    void setupViewerPanel();
    void setupInfoPanel();
    
    // Main layout
    QSplitter* m_mainSplitter;
    QWidget* m_controlPanel;
    QWidget* m_viewerPanel;
    QWidget* m_infoPanel;
    
    // Control widgets
    QGroupBox* m_imageGroup;
    QPushButton* m_loadImageBtn;
    QPushButton* m_loadDicomBtn;
    QProgressBar* m_loadingProgress;
    
    QGroupBox* m_renderingGroup;
    QComboBox* m_renderingModeCombo;
    QSlider* m_opacitySlider;
    QSlider* m_isoValueSlider;
    QLabel* m_opacityLabel;
    QLabel* m_isoValueLabel;
    QComboBox* m_presetCombo;
    QPushButton* m_resetViewBtn;
    
    QGroupBox* m_atlasGroup;
    QPushButton* m_loadAtlasBtn;
    QComboBox* m_atlasTypeCombo;
    QCheckBox* m_showAtlasCheck;
    QCheckBox* m_showLabelsCheck;
    QSlider* m_atlasOpacitySlider;
    QLabel* m_atlasOpacityLabel;
    
    // Viewer components
    QVTKOpenGLNativeWidget* m_vtkWidget;
    vtkSmartPointer<vtkRenderWindow> m_renderWindow;
    vtkSmartPointer<vtkRenderWindowInteractor> m_interactor;
    vtkSmartPointer<vtkInteractorStyleTrackballCamera> m_interactorStyle;
    
    // Info panel
    QTextEdit* m_imageInfoText;
    QTextEdit* m_atlasInfoText;
    QTextEdit* m_regionInfoText;
    
    // Core components
    std::unique_ptr<ImageLoader> m_imageLoader;
    std::unique_ptr<VolumeRenderer> m_volumeRenderer;
    std::unique_ptr<BrainAtlas> m_brainAtlas;
    
    // State
    bool m_imageLoaded;
    bool m_atlasLoaded;
    double m_currentOpacity;
    double m_currentIsoValue;
    double m_currentAtlasOpacity;
    
    // Helper methods
    void updateUI();
    void updateImageInfo();
    void updateAtlasInfo();
    void updateRegionInfo(int regionId, const QString& regionName);
    void connectSignals();
    void setupRenderingPresets();
    void setupAtlasTypes();
    void addAtlasActorsToRenderer();
    void removeAtlasActorsFromRenderer();
    QString formatSliderValue(double value, const QString& unit = "");
};

#endif // VIEWERWIDGET_H
