#ifndef VOLUMERENDERER_H
#define VOLUMERENDERER_H

#include <QObject>
#include <QColor>
#include <memory>

// VTK includes
#include <vtkSmartPointer.h>
#include <vtkImageData.h>
#include <vtkRenderer.h>
#include <vtkRenderWindow.h>
#include <vtkVolume.h>
#include <vtkVolumeMapper.h>
#include <vtkVolumeProperty.h>
#include <vtkColorTransferFunction.h>
#include <vtkPiecewiseFunction.h>
#include <vtkCamera.h>
#include <vtkActor.h>
#include <vtkPolyDataMapper.h>
#include <vtkContourFilter.h>
#include <vtkMarchingCubes.h>

class VolumeRenderer : public QObject
{
    Q_OBJECT

public:
    enum RenderingMode {
        VolumeRendering,
        SurfaceRendering,
        MixedRendering
    };

    explicit VolumeRenderer(QObject *parent = nullptr);
    ~VolumeRenderer();
    
    // Set input image data
    void setImageData(vtkSmartPointer<vtkImageData> imageData);
    
    // Get VTK renderer
    vtkSmartPointer<vtkRenderer> getRenderer() const { return m_renderer; }
    
    // Rendering mode control
    void setRenderingMode(RenderingMode mode);
    RenderingMode getRenderingMode() const { return m_renderingMode; }
    
    // Volume rendering properties
    void setOpacityTransferFunction(vtkSmartPointer<vtkPiecewiseFunction> opacity);
    void setColorTransferFunction(vtkSmartPointer<vtkColorTransferFunction> color);
    void setVolumeProperty(vtkSmartPointer<vtkVolumeProperty> property);
    
    // Surface rendering properties
    void setIsoValue(double value);
    void setSurfaceColor(const QColor& color);
    void setSurfaceOpacity(double opacity);
    
    // Camera control
    void resetCamera();
    void setViewDirection(double x, double y, double z);
    void setViewUp(double x, double y, double z);
    
    // Preset configurations
    void applyBrainPreset();
    void applyBonePreset();
    void applySkinPreset();
    void applyCustomPreset(const QString& presetName);
    
    // Background and lighting
    void setBackgroundColor(const QColor& color);
    void setAmbientLighting(double ambient);
    void setDiffuseLighting(double diffuse);
    void setSpecularLighting(double specular);
    
    // Utility methods
    void updateRendering();
    bool isImageLoaded() const { return m_imageData != nullptr; }
    
signals:
    void renderingUpdated();
    void renderingModeChanged(RenderingMode mode);
    void cameraChanged();

public slots:
    void onOpacityChanged(double opacity);
    void onIsoValueChanged(double isoValue);
    void onColorChanged(const QColor& color);

private:
    // VTK components
    vtkSmartPointer<vtkRenderer> m_renderer;
    vtkSmartPointer<vtkImageData> m_imageData;
    
    // Volume rendering
    vtkSmartPointer<vtkVolume> m_volume;
    vtkSmartPointer<vtkVolumeMapper> m_volumeMapper;
    vtkSmartPointer<vtkVolumeProperty> m_volumeProperty;
    vtkSmartPointer<vtkColorTransferFunction> m_colorFunction;
    vtkSmartPointer<vtkPiecewiseFunction> m_opacityFunction;
    
    // Surface rendering
    vtkSmartPointer<vtkActor> m_surfaceActor;
    vtkSmartPointer<vtkPolyDataMapper> m_surfaceMapper;
    vtkSmartPointer<vtkMarchingCubes> m_marchingCubes;
    
    // Current settings
    RenderingMode m_renderingMode;
    double m_isoValue;
    QColor m_surfaceColor;
    double m_surfaceOpacity;
    
    // Helper methods
    void initializeRenderer();
    void setupVolumeRendering();
    void setupSurfaceRendering();
    void createDefaultTransferFunctions();
    void updateVolumeRendering();
    void updateSurfaceRendering();
    void calculateImageStatistics(double& minValue, double& maxValue, double& meanValue);
};

#endif // VOLUMERENDERER_H
