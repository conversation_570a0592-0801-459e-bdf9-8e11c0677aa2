﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{606EA022-C81A-388D-A91A-347F8F6FC089}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>qt_ni</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">qt_ni.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">qt_ni</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">qt_ni.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">qt_ni</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">qt_ni.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">qt_ni</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">qt_ni.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">qt_ni</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_Debug;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.9.0/mingw_64/include/QtCore" /external:I "C:/Qt/6.9.0/mingw_64/include" /external:I "C:/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "C:/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "C:/Qt/6.9.0/mingw_64/include/QtGui" /external:I "C:/Qt/6.9.0/mingw_64/include/QtOpenGL"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;QT_OPENGL_LIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;QT_OPENGL_LIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_Debug;C:\Qt\6.9.0\mingw_64\include\QtCore;C:\Qt\6.9.0\mingw_64\include;C:\Qt\6.9.0\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.0\mingw_64\include\QtWidgets;C:\Qt\6.9.0\mingw_64\include\QtGui;C:\Qt\6.9.0\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_Debug;C:\Qt\6.9.0\mingw_64\include\QtCore;C:\Qt\6.9.0\mingw_64\include;C:\Qt\6.9.0\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.0\mingw_64\include\QtWidgets;C:\Qt\6.9.0\mingw_64\include\QtGui;C:\Qt\6.9.0\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target qt_ni</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/CMakeFiles/qt_ni_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/qt_ni_autogen/autouic_Debug.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;C:\Qt\6.9.0\mingw_64\lib\libQt6OpenGL.a;C:\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;C:\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;mingw32.lib;C:\Qt\6.9.0\mingw_64\lib\libQt6EntryPoint.a;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/lib/Debug/qt_ni.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/bin/Debug/qt_ni.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_Release;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.9.0/mingw_64/include/QtCore" /external:I "C:/Qt/6.9.0/mingw_64/include" /external:I "C:/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "C:/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "C:/Qt/6.9.0/mingw_64/include/QtGui" /external:I "C:/Qt/6.9.0/mingw_64/include/QtOpenGL"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;QT_OPENGL_LIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;QT_OPENGL_LIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_Release;C:\Qt\6.9.0\mingw_64\include\QtCore;C:\Qt\6.9.0\mingw_64\include;C:\Qt\6.9.0\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.0\mingw_64\include\QtWidgets;C:\Qt\6.9.0\mingw_64\include\QtGui;C:\Qt\6.9.0\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_Release;C:\Qt\6.9.0\mingw_64\include\QtCore;C:\Qt\6.9.0\mingw_64\include;C:\Qt\6.9.0\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.0\mingw_64\include\QtWidgets;C:\Qt\6.9.0\mingw_64\include\QtGui;C:\Qt\6.9.0\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target qt_ni</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/CMakeFiles/qt_ni_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/qt_ni_autogen/autouic_Release.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;C:\Qt\6.9.0\mingw_64\lib\libQt6OpenGL.a;C:\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;C:\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;mingw32.lib;C:\Qt\6.9.0\mingw_64\lib\libQt6EntryPoint.a;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/lib/Release/qt_ni.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/bin/Release/qt_ni.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_MinSizeRel;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.9.0/mingw_64/include/QtCore" /external:I "C:/Qt/6.9.0/mingw_64/include" /external:I "C:/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "C:/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "C:/Qt/6.9.0/mingw_64/include/QtGui" /external:I "C:/Qt/6.9.0/mingw_64/include/QtOpenGL"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;QT_OPENGL_LIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;QT_OPENGL_LIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_MinSizeRel;C:\Qt\6.9.0\mingw_64\include\QtCore;C:\Qt\6.9.0\mingw_64\include;C:\Qt\6.9.0\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.0\mingw_64\include\QtWidgets;C:\Qt\6.9.0\mingw_64\include\QtGui;C:\Qt\6.9.0\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_MinSizeRel;C:\Qt\6.9.0\mingw_64\include\QtCore;C:\Qt\6.9.0\mingw_64\include;C:\Qt\6.9.0\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.0\mingw_64\include\QtWidgets;C:\Qt\6.9.0\mingw_64\include\QtGui;C:\Qt\6.9.0\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target qt_ni</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/CMakeFiles/qt_ni_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/qt_ni_autogen/autouic_MinSizeRel.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;C:\Qt\6.9.0\mingw_64\lib\libQt6OpenGL.a;C:\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;C:\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;mingw32.lib;C:\Qt\6.9.0\mingw_64\lib\libQt6EntryPoint.a;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/lib/MinSizeRel/qt_ni.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/bin/MinSizeRel/qt_ni.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_RelWithDebInfo;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.9.0/mingw_64/include/QtCore" /external:I "C:/Qt/6.9.0/mingw_64/include" /external:I "C:/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "C:/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "C:/Qt/6.9.0/mingw_64/include/QtGui" /external:I "C:/Qt/6.9.0/mingw_64/include/QtOpenGL"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;QT_OPENGL_LIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;QT_OPENGL_LIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_RelWithDebInfo;C:\Qt\6.9.0\mingw_64\include\QtCore;C:\Qt\6.9.0\mingw_64\include;C:\Qt\6.9.0\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.0\mingw_64\include\QtWidgets;C:\Qt\6.9.0\mingw_64\include\QtGui;C:\Qt\6.9.0\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_RelWithDebInfo;C:\Qt\6.9.0\mingw_64\include\QtCore;C:\Qt\6.9.0\mingw_64\include;C:\Qt\6.9.0\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.0\mingw_64\include\QtWidgets;C:\Qt\6.9.0\mingw_64\include\QtGui;C:\Qt\6.9.0\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target qt_ni</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/CMakeFiles/qt_ni_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/qt_ni_autogen/autouic_RelWithDebInfo.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;C:\Qt\6.9.0\mingw_64\lib\libQt6OpenGL.a;C:\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;C:\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;mingw32.lib;C:\Qt\6.9.0\mingw_64\lib\libQt6EntryPoint.a;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/lib/RelWithDebInfo/qt_ni.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/bin/RelWithDebInfo/qt_ni.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\6932cf1e8580741708e9dc78e096639f\autouic_(CONFIG).stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\mainwindow.ui;C:\Qt\6.9.0\mingw_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_Debug.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\mainwindow.ui;C:\Qt\6.9.0\mingw_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_Release.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\mainwindow.ui;C:\Qt\6.9.0\mingw_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_MinSizeRel.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\mainwindow.ui;C:\Qt\6.9.0\mingw_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_RelWithDebInfo.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/desktop_projects/qt_ni/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/desktop_projects/qt_ni -BC:/Users/<USER>/Desktop/desktop_projects/qt_ni/build --check-stamp-file C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointMinGW32Target.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/desktop_projects/qt_ni/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/desktop_projects/qt_ni -BC:/Users/<USER>/Desktop/desktop_projects/qt_ni/build --check-stamp-file C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointMinGW32Target.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/desktop_projects/qt_ni/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/desktop_projects/qt_ni -BC:/Users/<USER>/Desktop/desktop_projects/qt_ni/build --check-stamp-file C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointMinGW32Target.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/desktop_projects/qt_ni/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/desktop_projects/qt_ni -BC:/Users/<USER>/Desktop/desktop_projects/qt_ni/build --check-stamp-file C:/Users/<USER>/Desktop/desktop_projects/qt_ni/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointMinGW32Target.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGL\Qt6OpenGLVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6OpenGLPrivate\Qt6OpenGLPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.9.0\mingw_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\4.0.2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\main.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\mainwindow.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\mainwindow.h" />
    <None Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\mainwindow.ui">
    </None>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\ImageLoader.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\ImageLoader.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\VolumeRenderer.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\VolumeRenderer.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\BrainAtlas.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\BrainAtlas.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\ViewerWidget.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\ViewerWidget.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_Debug\ui_mainwindow.h" />
    <None Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_Debug.stamp">
    </None>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_Release\ui_mainwindow.h" />
    <None Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_Release.stamp">
    </None>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_MinSizeRel\ui_mainwindow.h" />
    <None Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_MinSizeRel.stamp">
    </None>
    <ClCompile Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\include_RelWithDebInfo\ui_mainwindow.h" />
    <None Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\qt_ni_autogen\autouic_RelWithDebInfo.stamp">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\desktop_projects\qt_ni\build\ZERO_CHECK.vcxproj">
      <Project>{87FCF19B-09BA-3CFF-9247-ECAE3989A438}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>