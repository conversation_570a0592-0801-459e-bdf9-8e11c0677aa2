#ifndef BRAINATLAS_H
#define BRAINATLAS_H

#include <QObject>
#include <QString>
#include <QMap>
#include <QColor>
#include <QVector3D>
#include <memory>

// ITK includes
#include "itkImage.h"
#include "itkImageFileReader.h"
#include "itkResampleImageFilter.h"
#include "itkAffineTransform.h"
#include "itkLinearInterpolateImageFunction.h"
#include "itkNearestNeighborInterpolateImageFunction.h"

// VTK includes
#include <vtkSmartPointer.h>
#include <vtkImageData.h>
#include <vtkActor.h>
#include <vtkPolyDataMapper.h>
#include <vtkContourFilter.h>
#include <vtkLookupTable.h>
#include <vtkTextActor3D.h>

struct BrainRegion {
    int id;
    QString name;
    QString fullName;
    QColor color;
    bool visible;
    double opacity;
    vtkSmartPointer<vtkActor> actor;
    vtkSmartPointer<vtkTextActor3D> labelActor;
    QVector3D centroid;
};

class BrainAtlas : public QObject
{
    Q_OBJECT

public:
    // Atlas types
    enum AtlasType {
        AAL,           // Automated Anatomical Labeling
        AAL2,          // AAL version 2
        AAL3,          // AAL version 3
        Brodmann,      // Brodmann areas
        Harvard_Oxford, // Harvard-Oxford atlas
        Custom         // Custom atlas
    };

    // Image types for atlas
    using PixelType = unsigned short;
    using AtlasImageType = itk::Image<PixelType, 3>;
    using AtlasImagePointer = AtlasImageType::Pointer;
    
    // Transform type for registration
    using TransformType = itk::AffineTransform<double, 3>;
    using TransformPointer = TransformType::Pointer;

    explicit BrainAtlas(QObject *parent = nullptr);
    ~BrainAtlas();
    
    // Atlas loading
    bool loadAtlas(AtlasType type, const QString& atlasPath = "");
    bool loadCustomAtlas(const QString& atlasImagePath, const QString& labelFilePath);
    
    // Registration to subject space
    bool registerToSubjectSpace(vtkSmartPointer<vtkImageData> subjectImage);
    void setRegistrationTransform(TransformPointer transform);
    
    // Region management
    QList<BrainRegion> getAllRegions() const;
    BrainRegion getRegion(int regionId) const;
    BrainRegion getRegionByName(const QString& name) const;
    
    // Visualization control
    void setRegionVisibility(int regionId, bool visible);
    void setRegionColor(int regionId, const QColor& color);
    void setRegionOpacity(int regionId, double opacity);
    void setAllRegionsVisibility(bool visible);
    
    // Label display
    void setLabelsVisible(bool visible);
    void setLabelSize(double size);
    void setLabelColor(const QColor& color);
    
    // Get VTK actors for rendering
    QList<vtkSmartPointer<vtkActor>> getRegionActors() const;
    QList<vtkSmartPointer<vtkTextActor3D>> getLabelActors() const;
    
    // Atlas information
    QString getAtlasInfo() const;
    AtlasType getAtlasType() const { return m_atlasType; }
    bool isAtlasLoaded() const { return m_atlasImage.IsNotNull(); }
    
    // Coordinate queries
    BrainRegion getRegionAtPoint(double x, double y, double z) const;
    QVector3D getRegionCentroid(int regionId) const;
    
    // Preset configurations
    void applyDefaultColors();
    void applyFunctionalColors();
    void applyAnatomicalColors();
    
signals:
    void atlasLoaded(const QString& info);
    void regionClicked(int regionId, const QString& regionName);
    void registrationCompleted();
    void loadingError(const QString& error);

public slots:
    void onRegionVisibilityChanged(int regionId, bool visible);
    void onRegionColorChanged(int regionId, const QColor& color);

private:
    // Atlas data
    AtlasImagePointer m_atlasImage;
    vtkSmartPointer<vtkImageData> m_vtkAtlasImage;
    QMap<int, BrainRegion> m_regions;
    AtlasType m_atlasType;
    
    // Registration
    TransformPointer m_registrationTransform;
    bool m_isRegistered;
    
    // Visualization settings
    bool m_labelsVisible;
    double m_labelSize;
    QColor m_labelColor;
    vtkSmartPointer<vtkLookupTable> m_lookupTable;
    
    // VTK components
    vtkSmartPointer<vtkContourFilter> m_contourFilter;
    
    // Helper methods
    void initializeAtlas();
    void createRegionActors();
    void updateRegionActor(int regionId);
    void createLabelActors();
    void updateLabelActor(int regionId);
    void calculateRegionCentroids();
    QVector3D calculateCentroid(int regionId);
    
    // Atlas-specific loaders
    bool loadAALAtlas(const QString& atlasPath);
    bool loadBrodmannAtlas(const QString& atlasPath);
    bool loadHarvardOxfordAtlas(const QString& atlasPath);
    
    // Label file parsers
    bool parseAALLabels(const QString& labelFilePath);
    bool parseBrodmannLabels(const QString& labelFilePath);
    bool parseCustomLabels(const QString& labelFilePath);
    
    // Registration helpers
    void resampleAtlasToSubjectSpace(vtkSmartPointer<vtkImageData> subjectImage);
    void convertITKToVTK();
    
    // Color schemes
    void setupDefaultColorScheme();
    void setupFunctionalColorScheme();
    void setupAnatomicalColorScheme();
    QColor generateRegionColor(int regionId, const QString& regionName);
};

#endif // BRAINATLAS_H
