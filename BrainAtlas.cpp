#include "BrainAtlas.h"
#include <QDebug>
#include <QFile>
#include <QTextStream>
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QRandomGenerator>

// ITK includes
#include "itkImageToVTKImageFilter.h"
#include "itkIdentityTransform.h"

// VTK includes
#include <vtkProperty.h>
#include <vtkTextProperty.h>
#include <vtkImageThreshold.h>
#include <vtkMarchingCubes.h>
#include <vtkSmoothPolyDataFilter.h>
#include <vtkPolyDataNormals.h>

BrainAtlas::BrainAtlas(QObject *parent)
    : QObject(parent)
    , m_atlasType(AAL)
    , m_isRegistered(false)
    , m_labelsVisible(true)
    , m_labelSize(12.0)
    , m_labelColor(Qt::white)
{
    initializeAtlas();
}

BrainAtlas::~BrainAtlas()
{
}

bool BrainAtlas::loadAtlas(AtlasType type, const QString& atlasPath)
{
    m_atlasType = type;

    QString defaultPath = atlasPath;
    if (defaultPath.isEmpty()) {
        // Use default paths based on atlas type
        QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/atlases";

        switch (type) {
            case AAL:
                defaultPath = dataDir + "/aal/aal.nii";
                break;
            case AAL2:
                defaultPath = dataDir + "/aal2/aal2.nii";
                break;
            case AAL3:
                defaultPath = dataDir + "/aal3/aal3.nii";
                break;
            case Brodmann:
                defaultPath = dataDir + "/brodmann/brodmann.nii";
                break;
            case Harvard_Oxford:
                defaultPath = dataDir + "/harvard_oxford/harvard_oxford.nii";
                break;
            default:
                emit loadingError("Unknown atlas type");
                return false;
        }
    }

    try {
        // Load atlas image
        using ReaderType = itk::ImageFileReader<AtlasImageType>;
        auto reader = ReaderType::New();
        reader->SetFileName(defaultPath.toStdString());
        reader->Update();
        m_atlasImage = reader->GetOutput();

        // Convert to VTK
        convertITKToVTK();

        // Load corresponding labels
        QString labelPath = QFileInfo(defaultPath).absolutePath() + "/labels.txt";
        bool labelsLoaded = false;

        switch (type) {
            case AAL:
            case AAL2:
            case AAL3:
                labelsLoaded = parseAALLabels(labelPath);
                break;
            case Brodmann:
                labelsLoaded = parseBrodmannLabels(labelPath);
                break;
            case Harvard_Oxford:
                labelsLoaded = parseCustomLabels(labelPath);
                break;
            default:
                break;
        }

        if (!labelsLoaded) {
            qDebug() << "Warning: Could not load labels, using default names";
            // Create default regions based on unique values in atlas
            createDefaultRegions();
        }

        // Setup visualization
        createRegionActors();
        createLabelActors();
        calculateRegionCentroids();
        applyDefaultColors();

        emit atlasLoaded(getAtlasInfo());
        qDebug() << "Successfully loaded atlas:" << defaultPath;
        return true;

    } catch (const itk::ExceptionObject& ex) {
        QString error = QString("Failed to load atlas: %1").arg(ex.GetDescription());
        emit loadingError(error);
        qDebug() << error;
        return false;
    }
}

bool BrainAtlas::loadCustomAtlas(const QString& atlasImagePath, const QString& labelFilePath)
{
    m_atlasType = Custom;

    try {
        // Load atlas image
        using ReaderType = itk::ImageFileReader<AtlasImageType>;
        auto reader = ReaderType::New();
        reader->SetFileName(atlasImagePath.toStdString());
        reader->Update();
        m_atlasImage = reader->GetOutput();

        // Convert to VTK
        convertITKToVTK();

        // Load labels
        bool labelsLoaded = parseCustomLabels(labelFilePath);
        if (!labelsLoaded) {
            createDefaultRegions();
        }

        // Setup visualization
        createRegionActors();
        createLabelActors();
        calculateRegionCentroids();
        applyDefaultColors();

        emit atlasLoaded(getAtlasInfo());
        qDebug() << "Successfully loaded custom atlas:" << atlasImagePath;
        return true;

    } catch (const itk::ExceptionObject& ex) {
        QString error = QString("Failed to load custom atlas: %1").arg(ex.GetDescription());
        emit loadingError(error);
        qDebug() << error;
        return false;
    }
}

QList<BrainRegion> BrainAtlas::getAllRegions() const
{
    return m_regions.values();
}

BrainRegion BrainAtlas::getRegion(int regionId) const
{
    return m_regions.value(regionId, BrainRegion());
}

BrainRegion BrainAtlas::getRegionByName(const QString& name) const
{
    for (const auto& region : m_regions) {
        if (region.name == name || region.fullName == name) {
            return region;
        }
    }
    return BrainRegion();
}

void BrainAtlas::setRegionVisibility(int regionId, bool visible)
{
    if (m_regions.contains(regionId)) {
        m_regions[regionId].visible = visible;
        updateRegionActor(regionId);
        updateLabelActor(regionId);
    }
}

void BrainAtlas::setRegionColor(int regionId, const QColor& color)
{
    if (m_regions.contains(regionId)) {
        m_regions[regionId].color = color;
        updateRegionActor(regionId);
    }
}

void BrainAtlas::setRegionOpacity(int regionId, double opacity)
{
    if (m_regions.contains(regionId)) {
        m_regions[regionId].opacity = opacity;
        updateRegionActor(regionId);
    }
}

void BrainAtlas::setAllRegionsVisibility(bool visible)
{
    for (auto& region : m_regions) {
        region.visible = visible;
    }

    // Update all actors
    for (int regionId : m_regions.keys()) {
        updateRegionActor(regionId);
        updateLabelActor(regionId);
    }
}

void BrainAtlas::setLabelsVisible(bool visible)
{
    m_labelsVisible = visible;

    // Update all label actors
    for (int regionId : m_regions.keys()) {
        updateLabelActor(regionId);
    }
}

QList<vtkSmartPointer<vtkActor>> BrainAtlas::getRegionActors() const
{
    QList<vtkSmartPointer<vtkActor>> actors;
    for (const auto& region : m_regions) {
        if (region.actor && region.visible) {
            actors.append(region.actor);
        }
    }
    return actors;
}

QList<vtkSmartPointer<vtkTextActor3D>> BrainAtlas::getLabelActors() const
{
    QList<vtkSmartPointer<vtkTextActor3D>> actors;
    for (const auto& region : m_regions) {
        if (region.labelActor && region.visible && m_labelsVisible) {
            actors.append(region.labelActor);
        }
    }
    return actors;
}

QString BrainAtlas::getAtlasInfo() const
{
    if (!m_atlasImage) {
        return "No atlas loaded";
    }

    QString atlasTypeName;
    switch (m_atlasType) {
        case AAL: atlasTypeName = "AAL"; break;
        case AAL2: atlasTypeName = "AAL2"; break;
        case AAL3: atlasTypeName = "AAL3"; break;
        case Brodmann: atlasTypeName = "Brodmann"; break;
        case Harvard_Oxford: atlasTypeName = "Harvard-Oxford"; break;
        case Custom: atlasTypeName = "Custom"; break;
    }

    auto size = m_atlasImage->GetLargestPossibleRegion().GetSize();
    auto spacing = m_atlasImage->GetSpacing();

    QString info = QString(
        "Atlas Information:\n"
        "Type: %1\n"
        "Dimensions: %2 x %3 x %4\n"
        "Spacing: %.2f x %.2f x %.2f mm\n"
        "Number of regions: %5\n"
        "Registered: %6"
    ).arg(atlasTypeName)
     .arg(size[0]).arg(size[1]).arg(size[2])
     .arg(spacing[0]).arg(spacing[1]).arg(spacing[2])
     .arg(m_regions.size())
     .arg(m_isRegistered ? "Yes" : "No");

    return info;
}

void BrainAtlas::applyDefaultColors()
{
    setupDefaultColorScheme();
}

void BrainAtlas::initializeAtlas()
{
    m_lookupTable = vtkSmartPointer<vtkLookupTable>::New();
    m_contourFilter = vtkSmartPointer<vtkContourFilter>::New();
}

void BrainAtlas::createRegionActors()
{
    if (!m_vtkAtlasImage) return;

    for (auto& region : m_regions) {
        // Create threshold filter for this region
        auto threshold = vtkSmartPointer<vtkImageThreshold>::New();
        threshold->SetInputData(m_vtkAtlasImage);
        threshold->ThresholdBetween(region.id, region.id);
        threshold->SetInValue(1);
        threshold->SetOutValue(0);

        // Create surface using marching cubes
        auto marchingCubes = vtkSmartPointer<vtkMarchingCubes>::New();
        marchingCubes->SetInputConnection(threshold->GetOutputPort());
        marchingCubes->SetValue(0, 0.5);

        // Smooth the surface
        auto smoother = vtkSmartPointer<vtkSmoothPolyDataFilter>::New();
        smoother->SetInputConnection(marchingCubes->GetOutputPort());
        smoother->SetNumberOfIterations(15);
        smoother->SetRelaxationFactor(0.1);

        // Calculate normals
        auto normals = vtkSmartPointer<vtkPolyDataNormals>::New();
        normals->SetInputConnection(smoother->GetOutputPort());
        normals->ComputePointNormalsOn();
        normals->ComputeCellNormalsOn();

        // Create mapper
        auto mapper = vtkSmartPointer<vtkPolyDataMapper>::New();
        mapper->SetInputConnection(normals->GetOutputPort());
        mapper->ScalarVisibilityOff();

        // Create actor
        region.actor = vtkSmartPointer<vtkActor>::New();
        region.actor->SetMapper(mapper);
        region.actor->GetProperty()->SetColor(
            region.color.redF(), region.color.greenF(), region.color.blueF());
        region.actor->GetProperty()->SetOpacity(region.opacity);
        region.actor->GetProperty()->SetSpecular(0.3);
        region.actor->GetProperty()->SetSpecularPower(20);
    }
}

void BrainAtlas::convertITKToVTK()
{
    if (!m_atlasImage) return;

    try {
        using FilterType = itk::ImageToVTKImageFilter<AtlasImageType>;
        auto filter = FilterType::New();
        filter->SetInput(m_atlasImage);
        filter->Update();

        m_vtkAtlasImage = vtkSmartPointer<vtkImageData>::New();
        m_vtkAtlasImage->DeepCopy(filter->GetOutput());

    } catch (const itk::ExceptionObject& ex) {
        qDebug() << "Error converting atlas ITK to VTK:" << ex.GetDescription();
    }
}

void BrainAtlas::setupDefaultColorScheme()
{
    // Generate distinct colors for each region
    for (auto& region : m_regions) {
        region.color = generateRegionColor(region.id, region.name);
        region.opacity = 0.7;
        region.visible = true;
    }
}

QColor BrainAtlas::generateRegionColor(int regionId, const QString& regionName)
{
    // Use region ID to generate consistent colors
    QRandomGenerator generator(regionId);

    // Generate colors with good contrast
    int hue = (regionId * 137) % 360; // Golden angle for good distribution
    int saturation = 180 + (regionId * 23) % 75; // 180-255 range
    int value = 200 + (regionId * 17) % 55; // 200-255 range

    return QColor::fromHsv(hue, saturation, value);
}

void BrainAtlas::createDefaultRegions()
{
    if (!m_vtkAtlasImage) return;

    // Get unique values from atlas image
    m_vtkAtlasImage->GetScalarRange();
    double range[2];
    m_vtkAtlasImage->GetScalarRange(range);

    int minValue = static_cast<int>(range[0]);
    int maxValue = static_cast<int>(range[1]);

    // Create regions for each unique value (excluding background)
    for (int i = minValue; i <= maxValue; ++i) {
        if (i == 0) continue; // Skip background

        BrainRegion region;
        region.id = i;
        region.name = QString("Region_%1").arg(i);
        region.fullName = QString("Brain Region %1").arg(i);
        region.color = generateRegionColor(i, region.name);
        region.visible = true;
        region.opacity = 0.7;
        region.centroid = QVector3D(0, 0, 0); // Will be calculated later

        m_regions[i] = region;
    }

    qDebug() << "Created" << m_regions.size() << "default regions";
}

bool BrainAtlas::parseAALLabels(const QString& labelFilePath)
{
    QFile file(labelFilePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "Could not open AAL label file:" << labelFilePath;
        return false;
    }

    QTextStream in(&file);
    m_regions.clear();

    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (line.isEmpty() || line.startsWith("#")) continue;

        QStringList parts = line.split('\t');
        if (parts.size() >= 2) {
            bool ok;
            int id = parts[0].toInt(&ok);
            if (ok && id > 0) {
                BrainRegion region;
                region.id = id;
                region.name = parts[1];
                region.fullName = parts.size() > 2 ? parts[2] : parts[1];
                region.color = generateRegionColor(id, region.name);
                region.visible = true;
                region.opacity = 0.7;
                region.centroid = QVector3D(0, 0, 0);

                m_regions[id] = region;
            }
        }
    }

    qDebug() << "Loaded" << m_regions.size() << "AAL regions";
    return !m_regions.isEmpty();
}

bool BrainAtlas::parseBrodmannLabels(const QString& labelFilePath)
{
    // Similar to AAL but with Brodmann-specific parsing
    return parseAALLabels(labelFilePath); // For now, use same format
}

bool BrainAtlas::parseCustomLabels(const QString& labelFilePath)
{
    // Generic label file parser
    return parseAALLabels(labelFilePath); // For now, use same format
}

void BrainAtlas::createLabelActors()
{
    if (!m_atlasImage) return;

    for (auto& region : m_regions) {
        // Create 3D text actor for region label
        region.labelActor = vtkSmartPointer<vtkTextActor3D>::New();
        region.labelActor->SetInput(region.name.toStdString().c_str());

        // Set text properties
        auto textProperty = region.labelActor->GetTextProperty();
        textProperty->SetFontSize(static_cast<int>(m_labelSize));
        textProperty->SetColor(m_labelColor.redF(), m_labelColor.greenF(), m_labelColor.blueF());
        textProperty->SetBold(true);

        // Position will be set when centroid is calculated
        region.labelActor->SetPosition(region.centroid.x(), region.centroid.y(), region.centroid.z());
    }
}

void BrainAtlas::updateRegionActor(int regionId)
{
    if (!m_regions.contains(regionId) || !m_regions[regionId].actor) return;

    const auto& region = m_regions[regionId];
    auto actor = region.actor;

    // Update visibility
    actor->SetVisibility(region.visible);

    // Update color and opacity
    actor->GetProperty()->SetColor(
        region.color.redF(), region.color.greenF(), region.color.blueF());
    actor->GetProperty()->SetOpacity(region.opacity);
}

void BrainAtlas::updateLabelActor(int regionId)
{
    if (!m_regions.contains(regionId) || !m_regions[regionId].labelActor) return;

    const auto& region = m_regions[regionId];
    auto labelActor = region.labelActor;

    // Update visibility
    labelActor->SetVisibility(region.visible && m_labelsVisible);

    // Update text properties
    auto textProperty = labelActor->GetTextProperty();
    textProperty->SetFontSize(static_cast<int>(m_labelSize));
    textProperty->SetColor(m_labelColor.redF(), m_labelColor.greenF(), m_labelColor.blueF());
}

void BrainAtlas::calculateRegionCentroids()
{
    if (!m_vtkAtlasImage) return;

    for (auto& region : m_regions) {
        region.centroid = calculateCentroid(region.id);

        // Update label position
        if (region.labelActor) {
            region.labelActor->SetPosition(
                region.centroid.x(), region.centroid.y(), region.centroid.z());
        }
    }
}

QVector3D BrainAtlas::calculateCentroid(int regionId)
{
    if (!m_vtkAtlasImage) return QVector3D(0, 0, 0);

    // This is a simplified centroid calculation
    // In a real implementation, you would iterate through the image
    // and find the center of mass for the specific region

    int dims[3];
    m_vtkAtlasImage->GetDimensions(dims);
    double spacing[3];
    m_vtkAtlasImage->GetSpacing(spacing);
    double origin[3];
    m_vtkAtlasImage->GetOrigin(origin);

    // For now, return a position based on region ID
    // This should be replaced with actual centroid calculation
    double x = origin[0] + (dims[0] * spacing[0]) * 0.5;
    double y = origin[1] + (dims[1] * spacing[1]) * 0.5;
    double z = origin[2] + (dims[2] * spacing[2]) * 0.5;

    return QVector3D(x, y, z);
}
