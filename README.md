# Medical Image 3D Viewer with <PERSON> Atlas

A comprehensive Qt-based application for 3D visualization of medical images with brain atlas integration and region labeling capabilities.

## Features

### Core Functionality
- **Multi-format Support**: Load NIfTI (.nii, .nii.gz) and DICOM (.dcm, .dicom) files
- **DICOM Series**: Support for loading complete DICOM series from directories
- **3D Visualization**: Advanced volume and surface rendering using VTK
- **Interactive Navigation**: Rotate, zoom, and pan through 3D models from any angle

### Brain Atlas Integration
- **Multiple Atlas Types**: Support for AAL, AAL2, AAL3, Brodmann, and Harvard-Oxford atlases
- **Custom Atlas**: Load your own atlas images with custom label files
- **Region Labeling**: Automatic brain region identification and labeling
- **Color Coding**: Different colors for each brain region with customizable opacity
- **Template Registration**: Map standard brain templates to subject space

### Rendering Options
- **Volume Rendering**: Transparent 3D volume visualization
- **Surface Rendering**: Isosurface extraction and display
- **Mixed Rendering**: Combine volume and surface rendering
- **Multiple Presets**: Brain, bone, and skin visualization presets
- **Real-time Controls**: Adjust opacity, iso-values, and colors interactively

## Requirements

### Dependencies
- **Qt 6.x**: Cross-platform GUI framework
- **VTK 9.x**: Visualization Toolkit for 3D rendering
- **ITK 5.x**: Insight Toolkit for medical image processing
- **CMake 3.16+**: Build system
- **C++17**: Modern C++ standard

### System Requirements
- **OS**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **RAM**: Minimum 8GB, recommended 16GB for large datasets
- **GPU**: OpenGL 3.3+ compatible graphics card
- **Storage**: 1GB free space for application and sample data

## Installation

### Prerequisites
1. Install Qt 6.x from [Qt Official Website](https://www.qt.io/download)
2. Install VTK and ITK libraries (see platform-specific instructions below)
3. Install CMake 3.16 or later

### Windows
```bash
# Using vcpkg (recommended)
vcpkg install vtk itk

# Or download pre-built binaries from official websites
```

### macOS
```bash
# Using Homebrew
brew install vtk itk cmake qt6

# Using MacPorts
sudo port install vtk +qt6 +python39
sudo port install InsightToolkit
```

### Linux (Ubuntu/Debian)
```bash
# Install dependencies
sudo apt update
sudo apt install cmake build-essential qt6-base-dev qt6-tools-dev

# Build VTK and ITK from source (recommended for latest versions)
# Or use package manager versions:
sudo apt install libvtk9-dev libinsighttoolkit5-dev
```

### Building from Source
```bash
# Clone the repository
git clone <repository-url>
cd qt_ni

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build the application
cmake --build . --config Release

# Run the application
./qt_ni  # Linux/macOS
# or
qt_ni.exe  # Windows
```

## Usage

### Loading Medical Images

#### Single File
1. Click "Load NIfTI/DICOM File" or use File → Open Image (Ctrl+O)
2. Select a .nii, .nii.gz, .dcm, or .dicom file
3. Wait for the image to load and render

#### DICOM Series
1. Click "Load DICOM Series" or use File → Open DICOM Series (Ctrl+D)
2. Select a directory containing DICOM files
3. The application will automatically detect and load the series

### Brain Atlas Integration

#### Standard Atlases
1. Select atlas type from the dropdown (AAL, AAL2, AAL3, Brodmann, Harvard-Oxford)
2. Click "Load Atlas" or use File → Load Atlas (Ctrl+A)
3. The application will attempt to load the atlas from default locations

#### Custom Atlas
1. Select "Custom" from the atlas type dropdown
2. Click "Load Atlas"
3. Choose your atlas image file (.nii or .nii.gz)
4. Choose the corresponding label file (.txt or .csv)

### 3D Visualization Controls

#### Rendering Modes
- **Volume Rendering**: Transparent 3D visualization showing internal structures
- **Surface Rendering**: Solid surface extraction at specified iso-value
- **Mixed Rendering**: Combination of volume and surface rendering

#### Interactive Controls
- **Mouse Navigation**:
  - Left click + drag: Rotate view
  - Right click + drag: Zoom in/out
  - Middle click + drag: Pan view
  - Scroll wheel: Zoom in/out

#### Parameter Adjustment
- **Opacity Slider**: Control transparency of rendered surfaces
- **Iso Value Slider**: Adjust threshold for surface extraction
- **Atlas Opacity**: Control visibility of brain regions
- **Preset Selection**: Apply predefined rendering settings

### Brain Region Analysis
- **Region Selection**: Click on brain regions to see detailed information
- **Label Display**: Toggle region labels on/off
- **Color Coding**: Each region is automatically assigned a unique color
- **Region Information**: View region ID, name, and properties in the info panel

## File Formats

### Supported Image Formats
- **NIfTI**: .nii, .nii.gz (Neuroimaging Informatics Technology Initiative)
- **DICOM**: .dcm, .dicom (Digital Imaging and Communications in Medicine)

### Atlas Label Format
Label files should be tab-separated text files with the format:
```
# Region_ID    Region_Name    Full_Name
1             Precentral_L   Left Precentral Gyrus
2             Precentral_R   Right Precentral Gyrus
...
```

## Troubleshooting

### Common Issues

#### "VTK not found" Error
- Ensure VTK is properly installed and CMAKE_PREFIX_PATH includes VTK installation
- Check that VTK version is compatible (9.x recommended)

#### "ITK not found" Error
- Verify ITK installation and CMake configuration
- Ensure ITK version 5.x is installed

#### Rendering Performance Issues
- Check graphics drivers are up to date
- Reduce image resolution for large datasets
- Adjust rendering quality settings

#### Atlas Loading Failures
- Verify atlas files are in correct format
- Check file permissions and paths
- Ensure label files match atlas image values

### Performance Optimization
- Use GPU-accelerated volume rendering when available
- Reduce atlas opacity for better performance
- Close unused applications to free memory
- Use SSD storage for faster file loading

## Development

### Project Structure
```
qt_ni/
├── main.cpp              # Application entry point
├── mainwindow.h/cpp      # Main window implementation
├── ViewerWidget.h/cpp    # 3D viewer widget
├── ImageLoader.h/cpp     # Medical image loading
├── VolumeRenderer.h/cpp  # 3D rendering engine
├── BrainAtlas.h/cpp      # Brain atlas management
├── CMakeLists.txt        # Build configuration
└── README.md            # This file
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **VTK Community**: For the excellent 3D visualization toolkit
- **ITK Community**: For medical image processing capabilities
- **Qt Project**: For the cross-platform GUI framework
- **Medical Imaging Community**: For atlas data and standards

## Contact

For questions, issues, or contributions, please contact [<EMAIL>] or create an issue on the project repository.
