#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMenuBar>
#include <QStatusBar>
#include <QAction>
#include <QFileDialog>
#include <QMessageBox>
#include <QProgressBar>
#include <QLabel>

#include "ViewerWidget.h"

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onOpenImage();
    void onOpenDicomSeries();
    void onLoadAtlas();
    void onAbout();
    void onExit();
    void onImageLoaded(const QString& info);
    void onAtlasLoaded(const QString& info);
    void onRegionClicked(int regionId, const QString& regionName);
    void onLoadingError(const QString& error);
    void onStatusMessage(const QString& message);

private:
    void setupMenus();
    void setupStatusBar();
    void setupActions();
    void connectSignals();

    Ui::MainWindow *ui;
    ViewerWidget *m_viewerWidget;

    // Menu actions
    QAction *m_openImageAction;
    QAction *m_openDicomSeriesAction;
    QAction *m_loadAtlasAction;
    QAction *m_exitAction;
    QAction *m_aboutAction;

    // Status bar widgets
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;
};

#endif // MAINWINDOW_H
