#include "ViewerWidget.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QApplication>
#include <QStandardPaths>
#include <QDebug>

ViewerWidget::ViewerWidget(QWidget *parent)
    : QWidget(parent)
    , m_imageLoaded(false)
    , m_atlasLoaded(false)
    , m_currentOpacity(1.0)
    , m_currentIsoValue(500.0)
    , m_currentAtlasOpacity(0.7)
{
    // Initialize core components
    m_imageLoader = std::make_unique<ImageLoader>(this);
    m_volumeRenderer = std::make_unique<VolumeRenderer>(this);
    m_brainAtlas = std::make_unique<BrainAtlas>(this);

    setupUI();
    connectSignals();
    updateUI();
}

ViewerWidget::~ViewerWidget()
{
}

void ViewerWidget::setupUI()
{
    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);

    // Setup panels
    setupControlPanel();
    setupViewerPanel();
    setupInfoPanel();

    // Add panels to splitter
    m_mainSplitter->addWidget(m_controlPanel);
    m_mainSplitter->addWidget(m_viewerPanel);
    m_mainSplitter->addWidget(m_infoPanel);

    // Set splitter proportions
    m_mainSplitter->setStretchFactor(0, 0); // Control panel - fixed width
    m_mainSplitter->setStretchFactor(1, 1); // Viewer panel - expandable
    m_mainSplitter->setStretchFactor(2, 0); // Info panel - fixed width

    m_mainSplitter->setSizes({250, 600, 250});

    // Main layout
    auto mainLayout = new QHBoxLayout(this);
    mainLayout->addWidget(m_mainSplitter);
    mainLayout->setContentsMargins(5, 5, 5, 5);
}

void ViewerWidget::setupControlPanel()
{
    m_controlPanel = new QWidget();
    m_controlPanel->setMaximumWidth(280);
    m_controlPanel->setMinimumWidth(250);

    auto layout = new QVBoxLayout(m_controlPanel);

    // Image loading group
    m_imageGroup = new QGroupBox("Image Loading");
    auto imageLayout = new QVBoxLayout(m_imageGroup);

    m_loadImageBtn = new QPushButton("Load NIfTI/DICOM File");
    m_loadDicomBtn = new QPushButton("Load DICOM Series");
    m_loadingProgress = new QProgressBar();
    m_loadingProgress->setVisible(false);

    imageLayout->addWidget(m_loadImageBtn);
    imageLayout->addWidget(m_loadDicomBtn);
    imageLayout->addWidget(m_loadingProgress);

    // Rendering control group
    m_renderingGroup = new QGroupBox("Rendering Control");
    auto renderLayout = new QVBoxLayout(m_renderingGroup);

    // Rendering mode
    renderLayout->addWidget(new QLabel("Rendering Mode:"));
    m_renderingModeCombo = new QComboBox();
    renderLayout->addWidget(m_renderingModeCombo);

    // Preset selection
    renderLayout->addWidget(new QLabel("Preset:"));
    m_presetCombo = new QComboBox();
    renderLayout->addWidget(m_presetCombo);

    // Opacity control
    m_opacityLabel = new QLabel("Opacity: 100%");
    m_opacitySlider = new QSlider(Qt::Horizontal);
    m_opacitySlider->setRange(0, 100);
    m_opacitySlider->setValue(100);
    renderLayout->addWidget(m_opacityLabel);
    renderLayout->addWidget(m_opacitySlider);

    // Iso value control
    m_isoValueLabel = new QLabel("Iso Value: 500");
    m_isoValueSlider = new QSlider(Qt::Horizontal);
    m_isoValueSlider->setRange(0, 2000);
    m_isoValueSlider->setValue(500);
    renderLayout->addWidget(m_isoValueLabel);
    renderLayout->addWidget(m_isoValueSlider);

    // Reset view button
    m_resetViewBtn = new QPushButton("Reset View");
    renderLayout->addWidget(m_resetViewBtn);

    // Atlas group
    m_atlasGroup = new QGroupBox("Brain Atlas");
    auto atlasLayout = new QVBoxLayout(m_atlasGroup);

    // Atlas type selection
    atlasLayout->addWidget(new QLabel("Atlas Type:"));
    m_atlasTypeCombo = new QComboBox();
    atlasLayout->addWidget(m_atlasTypeCombo);

    m_loadAtlasBtn = new QPushButton("Load Atlas");
    atlasLayout->addWidget(m_loadAtlasBtn);

    // Atlas display options
    m_showAtlasCheck = new QCheckBox("Show Atlas Regions");
    m_showAtlasCheck->setChecked(true);
    atlasLayout->addWidget(m_showAtlasCheck);

    m_showLabelsCheck = new QCheckBox("Show Region Labels");
    m_showLabelsCheck->setChecked(true);
    atlasLayout->addWidget(m_showLabelsCheck);

    // Atlas opacity
    m_atlasOpacityLabel = new QLabel("Atlas Opacity: 70%");
    m_atlasOpacitySlider = new QSlider(Qt::Horizontal);
    m_atlasOpacitySlider->setRange(0, 100);
    m_atlasOpacitySlider->setValue(70);
    atlasLayout->addWidget(m_atlasOpacityLabel);
    atlasLayout->addWidget(m_atlasOpacitySlider);

    // Add groups to main layout
    layout->addWidget(m_imageGroup);
    layout->addWidget(m_renderingGroup);
    layout->addWidget(m_atlasGroup);
    layout->addStretch();

    // Setup combo box contents
    setupRenderingPresets();
    setupAtlasTypes();
}

void ViewerWidget::setupViewerPanel()
{
    m_viewerPanel = new QWidget();

    // Create VTK widget
    m_vtkWidget = new QVTKOpenGLNativeWidget(m_viewerPanel);

    // Setup VTK render window
    m_renderWindow = vtkSmartPointer<vtkRenderWindow>::New();
    m_vtkWidget->setRenderWindow(m_renderWindow);

    // Setup interactor
    m_interactor = m_renderWindow->GetInteractor();
    m_interactorStyle = vtkSmartPointer<vtkInteractorStyleTrackballCamera>::New();
    m_interactor->SetInteractorStyle(m_interactorStyle);

    // Add volume renderer to render window
    m_renderWindow->AddRenderer(m_volumeRenderer->getRenderer());

    // Layout
    auto layout = new QVBoxLayout(m_viewerPanel);
    layout->addWidget(m_vtkWidget);
    layout->setContentsMargins(0, 0, 0, 0);
}

void ViewerWidget::setupInfoPanel()
{
    m_infoPanel = new QWidget();
    m_infoPanel->setMaximumWidth(280);
    m_infoPanel->setMinimumWidth(250);

    auto layout = new QVBoxLayout(m_infoPanel);

    // Image info
    layout->addWidget(new QLabel("Image Information:"));
    m_imageInfoText = new QTextEdit();
    m_imageInfoText->setMaximumHeight(150);
    m_imageInfoText->setReadOnly(true);
    layout->addWidget(m_imageInfoText);

    // Atlas info
    layout->addWidget(new QLabel("Atlas Information:"));
    m_atlasInfoText = new QTextEdit();
    m_atlasInfoText->setMaximumHeight(150);
    m_atlasInfoText->setReadOnly(true);
    layout->addWidget(m_atlasInfoText);

    // Region info
    layout->addWidget(new QLabel("Selected Region:"));
    m_regionInfoText = new QTextEdit();
    m_regionInfoText->setMaximumHeight(100);
    m_regionInfoText->setReadOnly(true);
    layout->addWidget(m_regionInfoText);

    layout->addStretch();
}

void ViewerWidget::connectSignals()
{
    // Image loader signals
    connect(m_imageLoader.get(), &ImageLoader::imageLoaded,
            this, &ViewerWidget::imageLoaded);
    connect(m_imageLoader.get(), &ImageLoader::loadingError,
            this, &ViewerWidget::onImageLoadingError);
    connect(m_imageLoader.get(), &ImageLoader::loadingProgress,
            this, &ViewerWidget::onImageLoadingProgress);

    // Volume renderer signals
    connect(m_volumeRenderer.get(), &VolumeRenderer::renderingUpdated,
            [this]() { m_renderWindow->Render(); });

    // Brain atlas signals
    connect(m_brainAtlas.get(), &BrainAtlas::atlasLoaded,
            this, &ViewerWidget::atlasLoaded);
    connect(m_brainAtlas.get(), &BrainAtlas::loadingError,
            this, &ViewerWidget::onAtlasLoadingError);
    connect(m_brainAtlas.get(), &BrainAtlas::regionClicked,
            this, &ViewerWidget::onRegionClicked);

    // UI control signals
    connect(m_loadImageBtn, &QPushButton::clicked,
            this, &ViewerWidget::onLoadImageClicked);
    connect(m_loadDicomBtn, &QPushButton::clicked,
            this, &ViewerWidget::onLoadDicomSeriesClicked);
    connect(m_loadAtlasBtn, &QPushButton::clicked,
            this, &ViewerWidget::onLoadAtlasClicked);
    connect(m_resetViewBtn, &QPushButton::clicked,
            this, &ViewerWidget::onResetViewClicked);

    connect(m_renderingModeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ViewerWidget::onRenderingModeChanged);
    connect(m_presetCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ViewerWidget::onPresetChanged);

    connect(m_opacitySlider, &QSlider::valueChanged,
            this, &ViewerWidget::onOpacityChanged);
    connect(m_isoValueSlider, &QSlider::valueChanged,
            this, &ViewerWidget::onIsoValueChanged);
    connect(m_atlasOpacitySlider, &QSlider::valueChanged,
            this, &ViewerWidget::onAtlasOpacityChanged);

    connect(m_showAtlasCheck, &QCheckBox::toggled,
            this, &ViewerWidget::onShowAtlasToggled);
    connect(m_showLabelsCheck, &QCheckBox::toggled,
            this, &ViewerWidget::onShowLabelsToggled);
}

void ViewerWidget::setupRenderingPresets()
{
    m_renderingModeCombo->addItem("Volume Rendering", static_cast<int>(VolumeRenderer::VolumeRendering));
    m_renderingModeCombo->addItem("Surface Rendering", static_cast<int>(VolumeRenderer::SurfaceRendering));
    m_renderingModeCombo->addItem("Mixed Rendering", static_cast<int>(VolumeRenderer::MixedRendering));

    m_presetCombo->addItem("Brain");
    m_presetCombo->addItem("Bone");
    m_presetCombo->addItem("Skin");
    m_presetCombo->addItem("Custom");
}

void ViewerWidget::setupAtlasTypes()
{
    m_atlasTypeCombo->addItem("AAL", static_cast<int>(BrainAtlas::AAL));
    m_atlasTypeCombo->addItem("AAL2", static_cast<int>(BrainAtlas::AAL2));
    m_atlasTypeCombo->addItem("AAL3", static_cast<int>(BrainAtlas::AAL3));
    m_atlasTypeCombo->addItem("Brodmann", static_cast<int>(BrainAtlas::Brodmann));
    m_atlasTypeCombo->addItem("Harvard-Oxford", static_cast<int>(BrainAtlas::Harvard_Oxford));
    m_atlasTypeCombo->addItem("Custom", static_cast<int>(BrainAtlas::Custom));
}

// Public methods
void ViewerWidget::loadImage(const QString& filePath)
{
    QFileInfo fileInfo(filePath);
    QString extension = fileInfo.suffix().toLower();

    m_loadingProgress->setVisible(true);
    m_loadingProgress->setValue(0);

    bool success = false;
    if (extension == "nii" || extension == "gz") {
        success = m_imageLoader->loadNiftiFile(filePath);
    } else if (extension == "dcm" || extension == "dicom") {
        success = m_imageLoader->loadDicomFile(filePath);
    } else {
        emit loadingError("Unsupported file format: " + extension);
        m_loadingProgress->setVisible(false);
        return;
    }

    if (success) {
        // Set image data to volume renderer
        auto vtkImage = m_imageLoader->getVTKImage();
        m_volumeRenderer->setImageData(vtkImage);

        m_imageLoaded = true;
        updateUI();
        updateImageInfo();

        // Render the scene
        m_renderWindow->Render();

        emit statusMessage("Image loaded successfully");
    }

    m_loadingProgress->setVisible(false);
}

void ViewerWidget::loadDicomSeries(const QString& directoryPath)
{
    m_loadingProgress->setVisible(true);
    m_loadingProgress->setValue(0);

    bool success = m_imageLoader->loadDicomSeries(directoryPath);

    if (success) {
        // Set image data to volume renderer
        auto vtkImage = m_imageLoader->getVTKImage();
        m_volumeRenderer->setImageData(vtkImage);

        m_imageLoaded = true;
        updateUI();
        updateImageInfo();

        // Render the scene
        m_renderWindow->Render();

        emit statusMessage("DICOM series loaded successfully");
    }

    m_loadingProgress->setVisible(false);
}

void ViewerWidget::loadAtlas(BrainAtlas::AtlasType atlasType, const QString& atlasPath)
{
    bool success = m_brainAtlas->loadAtlas(atlasType, atlasPath);

    if (success) {
        m_atlasLoaded = true;
        updateUI();
        updateAtlasInfo();
        addAtlasActorsToRenderer();

        // Render the scene
        m_renderWindow->Render();

        emit statusMessage("Atlas loaded successfully");
    }
}

void ViewerWidget::loadCustomAtlas(const QString& atlasImagePath, const QString& labelFilePath)
{
    bool success = m_brainAtlas->loadCustomAtlas(atlasImagePath, labelFilePath);

    if (success) {
        m_atlasLoaded = true;
        updateUI();
        updateAtlasInfo();
        addAtlasActorsToRenderer();

        // Render the scene
        m_renderWindow->Render();

        emit statusMessage("Custom atlas loaded successfully");
    }
}

void ViewerWidget::setRenderingMode(VolumeRenderer::RenderingMode mode)
{
    m_volumeRenderer->setRenderingMode(mode);
    m_renderWindow->Render();
}

void ViewerWidget::resetView()
{
    m_volumeRenderer->resetCamera();
    m_renderWindow->Render();
}

bool ViewerWidget::isImageLoaded() const
{
    return m_imageLoaded;
}

bool ViewerWidget::isAtlasLoaded() const
{
    return m_atlasLoaded;
}

QString ViewerWidget::getImageInfo() const
{
    if (m_imageLoader) {
        return m_imageLoader->getImageInfo();
    }
    return "No image loaded";
}

QString ViewerWidget::getAtlasInfo() const
{
    if (m_brainAtlas) {
        return m_brainAtlas->getAtlasInfo();
    }
    return "No atlas loaded";
}

// Slot implementations
void ViewerWidget::onLoadImageClicked()
{
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "Load Medical Image",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "Medical Images (*.nii *.nii.gz *.dcm *.dicom);;NIfTI Files (*.nii *.nii.gz);;DICOM Files (*.dcm *.dicom);;All Files (*)"
    );

    if (!fileName.isEmpty()) {
        loadImage(fileName);
    }
}

void ViewerWidget::onLoadDicomSeriesClicked()
{
    QString dirName = QFileDialog::getExistingDirectory(
        this,
        "Select DICOM Series Directory",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation)
    );

    if (!dirName.isEmpty()) {
        loadDicomSeries(dirName);
    }
}

void ViewerWidget::onLoadAtlasClicked()
{
    int atlasTypeIndex = m_atlasTypeCombo->currentData().toInt();
    auto atlasType = static_cast<BrainAtlas::AtlasType>(atlasTypeIndex);

    if (atlasType == BrainAtlas::Custom) {
        // Load custom atlas
        QString atlasFile = QFileDialog::getOpenFileName(
            this,
            "Load Atlas Image",
            QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
            "NIfTI Files (*.nii *.nii.gz);;All Files (*)"
        );

        if (!atlasFile.isEmpty()) {
            QString labelFile = QFileDialog::getOpenFileName(
                this,
                "Load Atlas Labels",
                QFileInfo(atlasFile).absolutePath(),
                "Text Files (*.txt *.csv);;All Files (*)"
            );

            if (!labelFile.isEmpty()) {
                loadCustomAtlas(atlasFile, labelFile);
            }
        }
    } else {
        // Load standard atlas
        loadAtlas(atlasType);
    }
}

void ViewerWidget::onRenderingModeChanged()
{
    int modeIndex = m_renderingModeCombo->currentData().toInt();
    auto mode = static_cast<VolumeRenderer::RenderingMode>(modeIndex);
    setRenderingMode(mode);
}

void ViewerWidget::onResetViewClicked()
{
    resetView();
}

void ViewerWidget::onOpacityChanged(int value)
{
    m_currentOpacity = value / 100.0;
    m_opacityLabel->setText(QString("Opacity: %1%").arg(value));

    if (m_volumeRenderer) {
        m_volumeRenderer->setSurfaceOpacity(m_currentOpacity);
    }
}

void ViewerWidget::onIsoValueChanged(int value)
{
    m_currentIsoValue = value;
    m_isoValueLabel->setText(QString("Iso Value: %1").arg(value));

    if (m_volumeRenderer) {
        m_volumeRenderer->setIsoValue(m_currentIsoValue);
    }
}

void ViewerWidget::onAtlasOpacityChanged(int value)
{
    m_currentAtlasOpacity = value / 100.0;
    m_atlasOpacityLabel->setText(QString("Atlas Opacity: %1%").arg(value));

    if (m_brainAtlas && m_atlasLoaded) {
        // Update opacity for all atlas regions
        auto regions = m_brainAtlas->getAllRegions();
        for (const auto& region : regions) {
            m_brainAtlas->setRegionOpacity(region.id, m_currentAtlasOpacity);
        }
        m_renderWindow->Render();
    }
}

void ViewerWidget::onShowLabelsToggled(bool show)
{
    if (m_brainAtlas) {
        m_brainAtlas->setLabelsVisible(show);
        m_renderWindow->Render();
    }
}

void ViewerWidget::onShowAtlasToggled(bool show)
{
    if (m_brainAtlas && m_atlasLoaded) {
        m_brainAtlas->setAllRegionsVisibility(show);
        if (show) {
            addAtlasActorsToRenderer();
        } else {
            removeAtlasActorsFromRenderer();
        }
        m_renderWindow->Render();
    }
}

void ViewerWidget::onPresetChanged()
{
    QString preset = m_presetCombo->currentText();

    if (m_volumeRenderer && m_imageLoaded) {
        if (preset == "Brain") {
            m_volumeRenderer->applyBrainPreset();
        } else if (preset == "Bone") {
            m_volumeRenderer->applyBonePreset();
        } else if (preset == "Skin") {
            m_volumeRenderer->applySkinPreset();
        }
        m_renderWindow->Render();
    }
}

// Private slot implementations
void ViewerWidget::onImageLoadingProgress(int percentage)
{
    m_loadingProgress->setValue(percentage);
}

void ViewerWidget::onImageLoadingError(const QString& error)
{
    QMessageBox::critical(this, "Image Loading Error", error);
    m_loadingProgress->setVisible(false);
    emit loadingError(error);
}

void ViewerWidget::onAtlasLoadingError(const QString& error)
{
    QMessageBox::critical(this, "Atlas Loading Error", error);
    emit loadingError(error);
}

void ViewerWidget::onRegionClicked(int regionId, const QString& regionName)
{
    updateRegionInfo(regionId, regionName);
    emit regionClicked(regionId, regionName);
}

// Helper method implementations
void ViewerWidget::updateUI()
{
    // Enable/disable controls based on loaded state
    m_renderingGroup->setEnabled(m_imageLoaded);
    m_atlasGroup->setEnabled(true); // Atlas can be loaded independently

    // Update slider ranges based on image data
    if (m_imageLoaded && m_imageLoader) {
        // Update iso value slider range based on image intensity range
        // This would require getting image statistics from the loader
        // For now, use default range
    }
}

void ViewerWidget::updateImageInfo()
{
    if (m_imageInfoText) {
        m_imageInfoText->setText(getImageInfo());
    }
}

void ViewerWidget::updateAtlasInfo()
{
    if (m_atlasInfoText) {
        m_atlasInfoText->setText(getAtlasInfo());
    }
}

void ViewerWidget::updateRegionInfo(int regionId, const QString& regionName)
{
    if (m_regionInfoText && m_brainAtlas) {
        auto region = m_brainAtlas->getRegion(regionId);
        QString info = QString(
            "Region ID: %1\n"
            "Name: %2\n"
            "Full Name: %3\n"
            "Visible: %4\n"
            "Opacity: %5"
        ).arg(region.id)
         .arg(region.name)
         .arg(region.fullName)
         .arg(region.visible ? "Yes" : "No")
         .arg(region.opacity, 0, 'f', 2);

        m_regionInfoText->setText(info);
    }
}

void ViewerWidget::addAtlasActorsToRenderer()
{
    if (!m_brainAtlas || !m_atlasLoaded) return;

    auto renderer = m_volumeRenderer->getRenderer();

    // Add region actors
    auto regionActors = m_brainAtlas->getRegionActors();
    for (auto actor : regionActors) {
        renderer->AddActor(actor);
    }

    // Add label actors
    auto labelActors = m_brainAtlas->getLabelActors();
    for (auto labelActor : labelActors) {
        renderer->AddActor(labelActor);
    }
}

void ViewerWidget::removeAtlasActorsFromRenderer()
{
    if (!m_brainAtlas || !m_atlasLoaded) return;

    auto renderer = m_volumeRenderer->getRenderer();

    // Remove region actors
    auto regionActors = m_brainAtlas->getRegionActors();
    for (auto actor : regionActors) {
        renderer->RemoveActor(actor);
    }

    // Remove label actors
    auto labelActors = m_brainAtlas->getLabelActors();
    for (auto labelActor : labelActors) {
        renderer->RemoveActor(labelActor);
    }
}

QString ViewerWidget::formatSliderValue(double value, const QString& unit)
{
    if (unit.isEmpty()) {
        return QString::number(value, 'f', 1);
    } else {
        return QString("%1 %2").arg(value, 0, 'f', 1).arg(unit);
    }
}
